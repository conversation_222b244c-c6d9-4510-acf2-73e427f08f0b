<template>
  <div>
    <CommonTitle text="三色预警" @click.native="ssyjJump"></CommonTitle>
    <div class="wrap-container">
      <CommonTitle2 text="预警情况"></CommonTitle2>
      <div class="yjqk_box box">
        <div class="tabs" v-show="city == '金华市'">
          <div
            class="tab_item"
            v-for="(item, index) in tabList"
            @click="clickTab(index)"
            :class="tabIndex == index ? 'tab_active' : ''"
          >
            {{ item }}
          </div>
        </div>
        <div style="position: absolute; right: 40px" v-show="year == $currentYear" class="yearChange">
          <el-date-picker
            v-model="datas"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="queryData"
            :append-to-body="false"
          ></el-date-picker>
        </div>
      </div>
      <div
        style="width: 100%; height: 470px; text-align: center; font-size: 30px; color: #ccc"
        v-show="(tabIndex == 0 && chartData1.length == 0) || (tabIndex == 1 && chartData2.length == 0)"
      >
        暂无数据
      </div>
      <div
        v-show="(tabIndex == 0 && chartData1.length != 0) || (tabIndex == 1 && chartData2.length != 0)"
        class="box"
        id="chartSsyj"
        style="width: 1030px; height: 470px"
      ></div>
      <CommonTitle2 text="三书一函"></CommonTitle2>
      <div class="ssyh_box box">
        <div class="ssyh_item" v-for="(item, index) in ssyhList">
          <img :src="item.icon" alt="" width="110" height="100" />
          <div style="margin: 10px 0 0 0px">
            <div class="item_name">{{ item.name }}</div>
            <div class="item_bottom">
              <span class="item_value s-font-45 xt_font s-yellow">{{ item.value }}</span>
              <span style="margin-left: 10px; color: #ffffff" class="item_unit s-font-25">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import moment from 'moment'
import { getSsyh, getYjqkChartqy, getYjqkChartly } from '@/api/home'
import { getUrl } from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
  },
  data() {
    return {
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      tabIndex: 0,
      tabList: ['按区域', '按领域'],
      chartData1: [],
      chartData2: [],
      datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      ssyhList: [
        {
          icon: require('@/assets/common/zfbgs.png'),
          name: '执法报告书',
          value: 0,
          unit: '份',
        },
        {
          icon: require('@/assets/common/zfjys.png'),
          name: '执法建议书',
          value: 0,
          unit: '份',
        },
        {
          icon: require('@/assets/common/zfjds.png'),
          name: '执法督办书',
          value: 0,
          unit: '份',
        },
        {
          icon: require('@/assets/common/fxtsh.png'),
          name: '风险提示函',
          value: 0,
          unit: '份',
        },
      ],
    }
  },
  computed: {},
  created() {
    this.ssyjToken()
  },
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initApi(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.datas = this.$getYearList(year, 'day')
      this.initApi(localStorage.getItem('city'), year)
    })
    this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
  },
  methods: {
    initApi(city, year) {
      //初始化三色预警数据
      this.queryData()
    },
    ssyjToken() {
      getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
        if (res.code == 200) {
          this.token = res.data.url.split('?')[1].split('&')[0].replace('token=', '')
          this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
        }
      })
    },
    ssyjJump() {
      getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
        if (res.code == 200) {
          this.token = res.data.url.split('?')[1].split('&')[0].replace('token=', '')
          window.open(res.data.url)
        }
      })
    },
    queryData() {
      //  日期选择
      this.getYJQK()
      axios({
        method: 'get',
        url: process.env.VUE_APP_BASE_API + '/statistics/letters/statistics-by-type',
        params: {
          startTime: this.datas[0],
          endTime: this.datas[1],
          adcode: this.city == '金华市' ? '' : this.getAreaCode(this.city),
        },
        headers: {
          Authorization: 'Bearer ' + this.token,
        },
      }).then((res) => {
        let result = res.data.data
        if (result) {
          this.ssyhList[0].value = result.filter((a) => a.type == 'ENFORCEMENT_REPORT')[0].total
          this.ssyhList[1].value = result.filter((a) => a.type == 'ENFORCEMENT_PROPOSAL')[0].total
          this.ssyhList[2].value = result.filter((a) => a.type == 'ENFORCEMENT_SUPERVISION')[0].total
          this.ssyhList[3].value = result.filter((a) => a.type == 'RISK_WARNING')[0].total
        }
      })
    },
    getAreaCode(name) {
      let citylist = [
        { name: '金华市', value: '330700' },
        { name: '婺城区', value: '330702' },
        { name: '金东区', value: '330703' },
        { name: '武义县', value: '330723' },
        { name: '浦江县', value: '330726' },
        { name: '兰溪市', value: '330781' },
        { name: '义乌市', value: '330782' },
        { name: '东阳市', value: '330783' },
        { name: '磐安县', value: '330727' },
        { name: '开发区', value: '330700' },
        { name: '永康市', value: '330784' },
      ]
      return citylist.find((item) => item.name == name).value
    },
    getYJQK() {
      const request1 = axios({
        method: 'get',
        url: process.env.VUE_APP_BASE_API + '/statistics/warnings/statistics-by-district',
        params: {
          startTime: this.datas[0],
          endTime: this.datas[1],
          adcode: this.getAreaCode(this.city),
        },
        headers: {
          Authorization: 'Bearer ' + this.token,
        },
      }).then((res) => {
        let result = res.data.data
        this.chartData1 = result.map((item) => ({
          name: item.institution.name,
          value1: item.statistics.find((el) => el.level == 'BLUE').total,
          value2: item.statistics.find((el) => el.level == 'ORANGE').total,
          value3: item.statistics.find((el) => el.level == 'RED').total,
        }))
      })

      const request2 = axios({
        method: 'get',
        url: process.env.VUE_APP_BASE_API + '/statistics/warnings/sz/statistics-by-domain',
        params: {
          startTime: this.datas[0],
          endTime: this.datas[1],
          adcode: this.getAreaCode(this.city),
        },
        headers: {
          Authorization: 'Bearer ' + this.token,
        },
      }).then((res) => {
        let result = res.data.data
        this.chartData2 = result.map((item) => ({
          name: item.domain.name,
          value1: item.statistics.find((el) => el.level == 'BLUE').total,
          value2: item.statistics.find((el) => el.level == 'ORANGE').total,
          value3: item.statistics.find((el) => el.level == 'RED').total,
        }))
      })

      Promise.all([request1, request2])
        .then(() => {
          // 两个请求都已完成
          this.city == '金华市' ? this.clickTab(this.tabIndex) : this.getChart02('chartSsyj', this.chartData2)
        })
        .catch((error) => {
          console.error('请求失败:', error)
        })
    },
    clickTab(index) {
      this.tabIndex = index
      if (index == 0) {
        this.getChart02('chartSsyj', this.chartData1)
      } else {
        this.getChart02('chartSsyj', this.chartData2)
      }
    },
    getChart02(id, chartData) {
      console.log('1111111', chartData)
      let myEc = this.$echarts.init(document.getElementById(id))
      let xdata = []
      let ydata = [[], [], []]
      chartData.forEach((item) => {
        xdata.push(item.name)
        ydata[0].push(item.value1)
        ydata[1].push(item.value2)
        ydata[2].push(item.value3)
      })
      let legend = ['蓝色预警', '黄色预警', '红色预警']
      let color = ['76,152,251', '172,171,52', '245,102,121']
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '15%',
          bottom: '0%',
          containLabel: true,
        },
        legend: {
          top: 10,
          left: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 50,
          textStyle: {
            fontSize: 24,
            color: '#fff',
            padding: [3, 0, 0, 0],
          },
          // data: legend,
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 20,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [],
      }
      for (var i = 0; i < legend.length; i++) {
        option.series.push({
          name: legend[i],
          type: 'bar',
          stack: '总量',
          barWidth: '40%',
          label: {
            show: false,
            position: 'insideRight',
          },
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(' + color[i] + ',0.99)',
                },
                {
                  offset: 1,
                  color: 'rgba(' + color[i] + ',0)',
                },
              ]),
              barBorderRadius: 4,
            },
          },
          data: ydata[i],
        })
      }
      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: fit-content;
  margin-bottom: 40px;
  .box {
    height: 900px;
    padding: 20px 40px;
    box-sizing: border-box;
    display: flex;
    /* flex-shrink: 1; */
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    /deep/ .yearChange {
      .el-input__inner {
        height: 48px !important;
        background-color: #132c4e !important;
        border: 2px solid #afdcfb !important;
        color: #fff !important;
        border-radius: 15px !important;
      }
      .el-picker-panel {
        left: -260px !important;
      }
    }
  }
  .yjqk_box {
    height: 59px;
    position: relative;
    display: flex;
    justify-content: space-between;
  }
  .tabs {
    width: 350px;
    display: flex;
    justify-content: space-around;
  }

  .tab_item {
    width: 243px;
    height: 59px;
    line-height: 59px;
    font-size: 36px;
    color: #abceef;
    text-align: center;
  }

  .tab_active {
    color: #fff;
    //font-style: italic;
    font-weight: bold;
    background: url('@/assets/common/tab_bg.png');
    background-size: 100% 100%;
  }
  .ssyh_box {
    width: 100%;
    height: 270px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 20px 90px;
  }
  .ssyh_item {
    display: flex;
    width: 45%;
    margin-bottom: 10px;
  }
  .item_name {
    width: 200px;
    height: 35px;
    font-size: 30px;
    line-height: 35px;
    color: #d1d6df;
    //font-style: italic;
  }
  .s-yellow {
    background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }
  .s-blue {
    color: #34dfe3;
  }
  .xt_font {
    font-family: DINCondensed;
    //font-style: italic;
  }
}
</style>