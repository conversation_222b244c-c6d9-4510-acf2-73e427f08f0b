<template>
  <ygfDialog :visible='visible' width='1415px'>
    <div id="app">
      <div class="rw-title flex-between" style='position: relative;top: 40px;left: 40px'>
        <div class="fs-44 text-mid-yellow dialogTitle" id="rwTitle" style="margin-left: 20px;">指挥体系</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="close" @click="close()">×</div>
        <div class="search_box">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 40%;
            "
          >
            <input type="text" placeholder="请输入单位" v-model="val" />
            <span @click="initApi()">查询</span>
            <span @click="resetFun">重置</span>
          </div>
          <div><span @click="call_close=true">拨号</span></div>
        </div>
        <div class="table-bottom" style="height: 620px">
          <div class="th">
            <div class="th_td" style="flex: 0.15; text-align: center">序号</div>
            <div class="th_td" style="flex: 0.2; text-align: center">单位</div>
            <div class="th_td" style="flex: 0.2; text-align: center">职务</div>
            <div class="th_td" style="flex: 0.15; text-align: center">姓名</div>
            <div class="th_td" style="flex: 0.2; text-align: center">
              联系方式
            </div>
          </div>
          <div class="tbody" id="box">
            <div class="tr" v-for="(item,index) in tableData" :key="index">
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{index+1}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                {{(item.bm == null?"":item.bm) + (item.town == null?"":item.town)}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                {{item.zw}}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{item.name}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                <div
                  style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
                >
                  <span
                    class="iconPhone"
                    @click="openCall(item.phone)"
                    :style="{
                      cursor: !isCallSystemReady ? 'not-allowed' : 'pointer',
                      opacity: !isCallSystemReady ? 0.5 : 1
                    }"
                    :title="!isCallSystemReady ? '通话系统未就绪' : '语音通话'"
                  ></span>
                  <span
                    class="iconVideo"
                    @click="openVideo(item.phone)"
                    :style="{
                      cursor: !isCallSystemReady ? 'not-allowed' : 'pointer',
                      opacity: !isCallSystemReady ? 0.5 : 1
                    }"
                    :title="!isCallSystemReady ? '通话系统未就绪' : '视频通话'"
                  ></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="call_box" v-show="call_close">
        <div class="s-flex s-row-between s-font-30 s-c-white">
          <h2 style="font-style: italic">电话拨号</h2>
          <div style="top: 0" class="close" @click="call_close=false">×</div>
        </div>

        <!-- 通话系统状态显示 -->
        <div class="call-status-bar">
          <span v-if="isCallSystemReady" class="status-ready">
            <i class="el-icon-success"></i> 通话系统就绪
          </span>
          <span v-else class="status-not-ready">
            <i class="el-icon-loading"></i> 通话系统初始化中...
          </span>
        </div>

        <div class="s-flex s-row-between s-m-t-20">
          <input
            type="number"
            class="call_input"
            v-model="callnum"
            placeholder="请输入电话号码"
            maxlength="11"
          />
          <img
            style="cursor: pointer"
            src="/static/images/zhdd/call_x.png"
            alt=""
            @click="clearCallNum()"
          />
        </div>
        <ul class="call_num s-flex s-flex-wrap">
          <li
            @click="callNumFun(num)"
            :style="{cursor:callnum.length>=11?'no-drop':'pointer'}"
            v-for="num in call_list"
            :key="num"
          >
            {{num}}
          </li>
        </ul>
        <div class="s-flex s-row-between s-m-t-10">
          <span
            class="iconPhone1"
            @click="openCall(callnum)"
            :style="{
              cursor: (!isCallSystemReady || !callnum) ? 'not-allowed' : 'pointer',
              opacity: (!isCallSystemReady || !callnum) ? 0.5 : 1
            }"
          ></span>
          <span
            class="iconVideo1"
            @click="openVideo(callnum)"
            :style="{
              cursor: (!isCallSystemReady || !callnum) ? 'not-allowed' : 'pointer',
              opacity: (!isCallSystemReady || !callnum) ? 0.5 : 1
            }"
          ></span>
        </div>
      </div>
    </div>

    <!-- 通话管理器组件 -->
    <CallManager
      ref="callManager"
      :config="callConfig"
      remote-view-id="remoteView_zhtx_dialog_5g"
      self-view-id="selfView_zhtx_dialog_5g"
      @initialized="onCallManagerInitialized"
      @error="onCallManagerError"
      @call-ended="onCallEnded"
      @signed-out="onSignedOut"
    />
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import CallManager from '@/components/CallManager'
import { dutyListDetails } from '@/api/zhdd'
export default {
  name: 'index',
  props: ['visible'],
  components: {
    ygfDialog,
    CallManager
  },
  data() {
    return {
      val: "",
      tableDataAll: [],
      tableData: [],
      call_close: false,
      call_list: [1, 2, 3, 4, 5, 6, 7, 8, 9, "*", 0, "#"],
      callnum: "",
      // 通话配置
      callConfig: {
        tenantType: 1,
        ip: 'ygf.xzzfj.jinhua.gov.cn',
        port: '443',
        vccId: '100317',
        agentId: '1001',
        password: 'Zyzx@10086',
        loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
      },
      // 通话系统状态
      isCallSystemReady: false
    }
  },
  computed: {},
  mounted() {
    this.initApi();
  },
  methods: {
    // 拨号功能
    callNumFun(num) {
      if (this.callnum.length < 11) {
        this.callnum += num;
      } else {
        return;
      }
    },
    clearCallNum() {
      this.callnum = this.callnum.substring(0, this.callnum.length - 1);
    },
    // 指挥体系功能
    async initApi() {
      dutyListDetails({area:localStorage.getItem("city"),deptName:this.val}).then(res => {
        console.log(res);
        if (res.code == 200) {
          this.tableData = res.data.map(item => {return {
            bm: item.deptName,
            name: item.name,
            zw: item.duties,
            phone: item.phone,
            town: item.town
          }})
        }
      })
    },
    filterTableFun() {
      if (this.val == "") {
        this.$message({
          message: "部门不能为空",
          type: "warning",
        });
        return;
      }
      let filterArr = this.tableDataAll.filter((a) => {
        return a.bm.indexOf(this.val) > 0;
      });
      this.tableData = filterArr;
    },
    resetFun() {
      this.val = "";
      // this.tableData = this.tableDataAll;
      this.initApi();
    },
    // 通话管理器事件处理
    onCallManagerInitialized() {
      this.isCallSystemReady = true
      console.log('通话管理器初始化成功')
    },

    onCallManagerError(error) {
      this.isCallSystemReady = false
      console.error('通话管理器错误:', error)
      this.$message.error('通话系统初始化失败')
    },

    onCallEnded() {
      console.log('通话已结束')
    },

    onSignedOut() {
      this.isCallSystemReady = false
      console.log('已退出通话系统')
    },

    // 发起语音通话
    openCall(phone) {
      if (!phone || phone === '') {
        this.$message.info('此人暂未录入手机号码')
        return
      }

      if (!this.isCallSystemReady) {
        this.$message.warning('通话系统未就绪，请稍后再试')
        return
      }

      if (this.$refs.callManager) {
        this.$refs.callManager.makeVoiceCall(phone)
        // 关闭拨号面板
        this.call_close = false
      }
    },

    // 发起视频通话
    openVideo(phone) {
      if (!phone || phone === '') {
        this.$message.info('此人暂未录入手机号码')
        return
      }

      if (!this.isCallSystemReady) {
        this.$message.warning('通话系统未就绪，请稍后再试')
        return
      }

      if (this.$refs.callManager) {
        this.$refs.callManager.makeVideoCall(phone)
        // 关闭拨号面板
        this.call_close = false
      }
    },

    close() {
      this.$emit('close')
    },
  },
  watch: {}
}
</script>

<style scoped>
body {
  margin: 0;
  padding: 0;
}
#app {
  width: 1600px;
  height: 880px;
  position: relative;
  background: url("@/assets/zhdd/dialogBg.png") no-repeat;
  background-size: 100% 100%;
}
.close {
  position: absolute;
  right: -30px;
  top: -90px;
  color: #fff;
  font-size: 68px;
  margin-right: 40px;
  cursor: pointer;
}
.content {
  width: 94%;
  position: absolute;
  top: 135px;
  left: 45px;
  height: 80%;
}
.search_box {
  display: flex;
  justify-content: space-between;
}
input {
  padding-left: 20px;
  font-size: 28px;
  width: 300px;
  height: 50px;
  margin-right: 10px;
  background: transparent;
  border: 1px solid #2ba5b0;
  color: #ccc;
}
.search_box span {
  padding: 6px 22px;
  border: 1px solid #2ba5b0;
  border-radius: 10px;
  background: rgba(43, 165, 176, 0.2);
  cursor: pointer;
  font-size: 32px;
  color: #fff;
}
.tr {
  -webkit-animation: none !important;
}
.call_box {
  width: 750px;
  height: 688px;
  background-color: rgba(3, 27, 60, 0.9);
  box-shadow: inset 0px 0px 40px 0px rgba(35, 154, 228, 0.8);
  border-radius: 20px;
  position: absolute;
  top: 85px;
  left: 300px;
  padding: 20px;
}

.call-status-bar {
  margin: 15px 0;
  text-align: center;

  .status-ready {
    color: #67c23a;
    font-size: 14px;

    i {
      margin-right: 5px;
    }
  }

  .status-not-ready {
    color: #e6a23c;
    font-size: 14px;

    i {
      margin-right: 5px;
      animation: rotating 1s linear infinite;
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.call_input {
  width: 500px;
  height: 61px;
  line-height: 61px;
  font-size: 68px;
  box-sizing: border-box;
  padding: 0 4px;
  color: #fff;
  background: none;
  border: none;
  outline: none;
}
.call_num li {
  list-style: none;
  width: 220px;
  height: 80px;
  line-height: 80px;
  font-size: 48px;
  color: #fff;
  text-align: center;
  background-color: #053564;
  border-radius: 10px;
  border: solid 2px #074a8c;
  margin: 11px 8px 11px 14px;
  float: left;
  cursor: pointer;
}
.call_num li:hover {
  background-color: #0d59a0;
}
.iconPhone1 {
  cursor: pointer;
  background: #51c422;
  padding: 8px 15px;
  border-radius: 10px;
  height: 80px;
  width: 300px;
  text-align: center;
  line-height: 100px;
}

.iconPhone1::after {
  content: "";
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url("@/assets/zhdd/icon_call.png") no-repeat;
  background-size: cover;
}

.iconVideo1 {
  text-align: center;
  cursor: pointer;
  background: #22a0c4;
  padding: 8px 15px;
  border-radius: 10px;
  height: 80px;
  width: 300px;
  line-height: 100px;
  cursor: pointer;
}

.iconVideo1::after {
  content: "";
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url("@/assets/zhdd/video.png") no-repeat;
  background-size: cover;
}
.call_input::-webkit-outer-spin-button,
.call_input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.iconPhone::after {
  background: url("@/assets/zhdd/icon_call.png") no-repeat;
  background-size: cover;
}

.iconVideo::after {
  background: url("@/assets/zhdd/video.png") no-repeat;
  background-size: cover;
}
.table-bottom {
  width: 100%;
  height: 385px;
  padding: 10px;
  box-sizing: border-box;
}

.table-bottom .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 28px;
  line-height: 60px;
  color: #ffffff;
}

.table-bottom .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table-bottom .tbody {
  width: 100%;
  height: calc(100% - 30px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table-bottom .tbody:hover {
  overflow-y: auto;
}

.table-bottom .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table-bottom .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table-bottom .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 95px;
  line-height: 95px;
  font-size: 28px;
  color: #ffffff;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table-bottom .tr:nth-child(2n) {
  background: rgba(50, 134, 248, 0.2);
}

.table-bottom .tr:nth-child(2n + 1) {
  background: rgba(50, 134, 248, 0.12);
}

.table-bottom .tr:hover {
  background-color: #0074da75;
}

.table-bottom .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

</style>