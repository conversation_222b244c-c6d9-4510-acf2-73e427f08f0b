.spinner {
  display: inline-block;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
  vertical-align: middle;
}
.spinner .spinner-text {
  font-size: 12px;
  border: 0px;
  margin: 0;
  padding: 0 2px;
  vertical-align: baseline;
}
.spinner-arrow {
  background-color: #F2F2F2;
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  width: 18px;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
  width: 100%;
  height: 50%;
  color: #444;
  outline-style: none;
}
.spinner-arrow-hover {
  background-color: #e6e6e6;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-arrow-up:hover,
.spinner-arrow-down:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #e6e6e6;
}
.textbox-icon-disabled .spinner-arrow-up:hover,
.textbox-icon-disabled .spinner-arrow-down:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
  background-color: #F2F2F2;
  cursor: default;
}
.spinner .textbox-icon-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
}
.spinner {
  border-color: #D4D4D4;
}
