<template>
  <div>
    <CommonTitle text='执法态势' @click.native="$router.push('/zfts')"></CommonTitle>
    <div class='wrap-container'>
      <CommonTitle2 text='行政检查'></CommonTitle2>
      <div class="xzjc_box box">
        <div class="xzjc_top">
          <div style="display: flex" class='xzjc_top_item'>
            <div class='xzjc_top_item_left'>检查总量</div>
            <div class='xzjc_top_item_right'>
              <div class='xzjc_top_item_right_number'>{{jcTotal}}</div>
              <div class='xzjc_top_item_right_unit'>万次</div>
            </div>
          </div>
          <div style="display: flex" class='xzjc_top_item'>
            <div class='xzjc_top_item_left'>综合查一次</div>
            <div class='xzjc_top_item_right'>
              <div class='xzjc_top_item_right_number'>{{lhjcTotal}}</div>
              <div class='xzjc_top_item_right_unit'>万次</div>
            </div>
          </div>
        </div>
        <div class="xzjc_middle">
          <div v-for="(item,index) in xzjcList">
            <div>
              <div
                :id="'echarts'+index"
                style="width: 200px; height: 220px"
              ></div>
              <br />
              <span style="white-space: nowrap">{{item.label}}</span>
            </div>
          </div>
        </div>
      </div>
      <CommonTitle2 text='行政处罚案件'></CommonTitle2>
      <div class="box">
        <div style="position: relative;bottom: 83px;left: 697px; z-index: 888" v-show="year == $currentYear" class='yearChange'>
          <el-date-picker
            v-model="datas"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            @change="(res) => xzcfaj(res,'')"
            :append-to-body='false'
          >
          </el-date-picker>
        </div>
        <div
          v-show="chartData1.length==0"
          class="s-font-30 s-c-white s-text-center"
          style="margin-top: 200px"
        >
          暂无数据
        </div>
        <div id="chart01" style="width: 100%; height: 445px;position: relative;bottom: 60px"></div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import 'echarts-liquidfill'
import CountTo from 'vue-count-to';
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
    CountTo
  },
  data() {
    return {
      datas: [
        new Date().getFullYear() + "-01",
        moment(new Date()).format("YYYY-MM"),
      ],
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      jcTotal: "00000",
      lhjcTotal: "00000",
      xzjcList: [],
      chartData1: []
    }
  },
  computed: {

  },
  created() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.datas = this.$getYearList(year);
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city, year) {
      this.datas = this.$getYearList(year);
      this.xzcfaj(this.datas, year);
      indexApi("/csdn_yjyp3", { area_code: city, sjwd2: year }).then((res) => {
        this.jcTotal = this.getDataValue(res.data, "检查总量");
        this.lhjcTotal = this.getDataValue(res.data, "跨部门");
        this.xzjcList = this.processData(res.data);
        setTimeout(() => {
          this.xzjcList.forEach((a, i) => {
            this.getliquidFill(`echarts${i}`, a);
          });
        }, 500);
      });
    },
    getDataValue(data, label) {
      return data.find((a) => a.label.includes(label)).num;
    },
    processData(data) {
      let list = [];
      data.forEach((a) => {
        if (!a.label.includes("占比") && !a.label.includes("检查总量") && !a.label.includes("跨部门联合检查")) {
          list.push({ label: a.label, num: a.num, unit: a.unit });
        } else if (a.label.includes("占比")) {
          let index = list.findIndex(c => c.label === a.label.replace("占比", ""));
          if (index !== -1) {
            list[index].zb = a.num;
          }
        }
      });
      return list;
    },
    getliquidFill(id, chartData) {
      this.$echarts.init(document.getElementById(id)).dispose();
      let myEc = this.$echarts.init(document.getElementById(id));
      var arr = ["middleLost", 0.6, "今日完成进度"];
      let option = {
        title: {
          top: "45%",
          left: "center",
          text: "",
          textStyle: {
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            fontSize: 28,
          },
          subtext: chartData.num + chartData.unit,
          subtextStyle: {
            color: "#fff",
            fontSize: 28,
          },
        },
        tooltip: {
          trigger: "item",
          backgroundColor: "rgba(51, 51, 51, 0.7)",
          borderWidth: 0,
          axisPointer: {
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: "white",
            fontSize: "24",
          },
          formatter: function (res) {
            if (res.componentSubType == "liquidFill") {
              return res.seriesName + ": " + chartData.zb + "%";
            }
          },
        },
        series: [
          {
            type: "liquidFill",
            itemStyle: {
              opacity: 0.95,
              shadowBlur: 50,
              shadowColor: 'rgba(0, 0, 0, 0.4)'
            },
            name: chartData.label,
            data: [0.6,0.5,0.4,0.3],
            // [
            //   {
            //     value: chartData.num / 10,
            //     value: 0.5,
            //     itemStyle: {
            //       normal: {
            //         color: "#14a4ff",
            //         opacity: 0.6,
            //       },
            //     },
            //   },
            // ],
            color: ["#14a4ff"],
            center: ["48%", "60%"],
            radius: "75%",
            backgroundStyle: {
              color: "#22508f87",
            },
            label: {
              normal: {
                formatter: "",
                textStyle: {
                  fontSize: 24,
                },
              },
            },
            outline: {
              show: true,
              borderDistance: 8,
              itemStyle: {
                color: 'none',
                borderColor: '#294D99',
                borderWidth: 8,
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.25)'
              }
            },
          },
        ],
      };
      myEc.setOption(option);
      myEc.getZr().on("mousemove", (param) => {
        myEc.getZr().setCursorStyle("default");
      });
    },
    //行政处罚案件
    xzcfaj(datas,year) {
      let city = localStorage.getItem("city")
      city == '金华市'?this.dataFun(datas,year):this.dataFun2(datas,city,year)
    },
    dataFun(e,year) {
      indexApi("/csdn_yjyp7", {
        sjwd1: e[0].replace(/-/g, ""),
        sjwd3: e[1].replace(/-/g, ""),
        sjwd2: year
      }).then((responce) => {
        let res = responce.data
        let legends = [];
        if (res.length > 0) {
          let citydata = [
            { city: "婺城区" },
            { city: "金东区" },
            { city: "兰溪市" },
            { city: "东阳市" },
            { city: "义乌市" },
            { city: "永康市" },
            { city: "浦江县" },
            { city: "武义县" },
            { city: "磐安县" },
            { city: "开发区" },
          ];
          res.map((a) => {
            !legends.includes(a.label) && legends.push(a.label);
          });
          citydata.forEach((ele, i) => {
            let filtArr = res.filter((a) => a.area_code == ele.city);
            filtArr.map((el) => {
              ele[el.label] = el.num;
            });
          });
          this.chartData1 = citydata;
        } else {
          this.chartData1 = [];
        }
        this.getChart01("chart01", this.chartData1, legends);
      });
    },
    dataFun2(e,city,year) {
      indexApi("/csdn_yjyp72", {
        qxwd: city,
        sjwd1: e[0].replace(/-/g, ""),
        sjwd3: e[1].replace(/-/g, ""),
        sjwd2: year
      }).then((responce) => {
        let res = responce.data
        let legends = [];
        if (res.length > 0) {
          let citydata = [
            { city: "交通运输" },
            { city: "卫生健康" },
            { city: "市场监管" },
            { city: "应急管理" },
            { city: "文化市场" },
            { city: "生态环境" },
            { city: "自然资源" },
          ];
          res.map((a) => {
            !legends.includes(a.label) && legends.push(a.label);
          });
          citydata.forEach((ele, i) => {
            let filtArr = res.filter((a) => a.ywwd2 == ele.city);
            filtArr.map((el) => {
              ele[el.label] = el.num;
            });
          });
          this.chartData1 = citydata;
        } else {
          this.chartData1 = [];
        }
        this.getChart01("chart01", this.chartData1, legends);
      });
    },
    getChart01(id, chartData, legends) {
      this.$echarts.init(document.getElementById(id)).dispose();
      let myEc = this.$echarts.init(document.getElementById(id));
      console.log(chartData);
      let xdata = [];
      let ydata = [[], []];
      chartData.forEach((item) => {
        xdata.push(item.city);
        ydata[0].push(item["一般程序案件数量"] || 0);
        ydata[1].push(item["简易程序案件数量"] || 0);
        // ydata[2].push(item["行政处罚案件数量"] || 0);
      });
      let legend = legends;
      let color = ["245,102,121", "172,171,52", "76,152,251"];
      let seriesData = legends.map((ele, index) => {
        return {
          name: ele,
          type: "bar",
          barWidth: "25%",
          stack: "总量",
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(" + color[index] + ",0.99)",
                },
                {
                  offset: 1,
                  color: "rgba(" + color[index] + ",0)",
                },
              ]),
              barBorderRadius: 4,
            },
          },
          data: ydata[index],
        };
      });
      let option = {
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(51, 51, 51, 0.7)",
          borderWidth: 0,
          axisPointer: {
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            color: "white",
            fontSize: "24",
          },
          formatter: function (params) {
            let total = params[0].data + params[1].data;
            let yb =
              total == 0
                ? "100"
                : ((params[0].data * 100) / total).toFixed(1);
            let jy =
              total == 0
                ? "100"
                : ((params[1].data * 100) / total).toFixed(1);
            return (
              params[0].name +
              " ：<br />" +
              "行政处罚案件数量：" +
              total +
              "件<br />" +
              params[0].seriesName +
              " ：" +
              params[0].data +
              "件<br />" +
              params[1].seriesName +
              " ：" +
              params[1].data +
              "件<br />" +
              "一般程序案件占比：" +
              yb +
              "%<br />" +
              "简易程序案件占比：" +
              jy +
              "%"
            );
          },
        },
        grid: {
          left: "5%",
          right: "5%",
          top: "28%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          data: legends,
          top: 60,
          left: "center",
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 50,
          textStyle: {
            fontSize: 24,
            color: "#D6E7F9",
            padding: [3, 0, 0, 0],
          },
        },
        xAxis: [
          {
            type: "category",
            data: xdata,
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 24,
              },
            },
          },
          {
            type: "category",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: xdata,
          },
        ],
        yAxis: [
          {
            name: "",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 24,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: seriesData,
      };
      myEc.setOption(option);
      myEc.getZr().on("mousemove", (param) => {
        myEc.getZr().setCursorStyle("default");
      });
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: fit-content;
  .box {
    padding: 20px 40px;
    box-sizing: border-box;
    position: relative;
    /deep/ .yearChange {
      .el-input__inner {
        height: 48px !important;
        background-color: #132c4e !important;
        border: 2px solid #afdcfb !important;
        color: #fff !important;
        border-radius: 15px !important;
      }
      .el-picker-panel {
        top: 200px !important;
      }
    }
  }
  .xzjc_top {
    white-space: nowrap;
    width: 100%;
    display: flex;
    justify-content: space-around;
    font-size: 30px;
    color: #ffffff;
    line-height: 60px;
    .xzjc_top_item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      .xzjc_top_item_left {
        flex: 1;
        height: 50px;
        line-height: 50px;
        background: rgba(11,100,195,0.5);
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 28px;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
      .xzjc_top_item_right {
        flex: 1;
        height: 50px;
        line-height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        .xzjc_top_item_right_number {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 28px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          background: linear-gradient(90deg, #FFF0B5 100%, #FFD52E 100%);
          -webkit-background-clip: text;
          color: transparent;
        }
        .xzjc_top_item_right_unit {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 28px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
  .count_box {
    display: flex;
    margin: 0 3px;
  }
  .count-toNum {
    width: 40px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    font-size: 43px;
    background: url("@/assets/common/sz-bg.png");
    background-size: 100% 100%;
  }
  .xzjc_middle {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
    //margin-top: 25px;
  }

  .xzjc_middle > div {
    flex: 1;
    //background: url("@/assets/common/percent_bg.png") no-repeat 0px 72px;
    //background-size: 95%;
    position: relative;
    bottom: 10px;
  }
}
</style>
