import { requestThirdParty } from '@/utils/request'
import CryptoJS from 'crypto-js'

/**
 * 第三方登录接口
 * @param {Object} params - 登录参数
 * @param {string} params.tenantCode - 租户代码
 * @param {string} params.account - 账户
 * @param {string} params.scope - 作用域
 * @param {Object} signHeaders - 签名头部信息
 * @param {string} signHeaders.vtxSignAppKey - 应用密钥
 * @param {string} signHeaders.vtxSignTimestamp - 时间戳
 * @param {string} signHeaders.vtxSign - 签名
 * @returns {Promise} 返回登录结果
 */
export function loginByThird(params, signHeaders) {
  const { tenantCode, account, scope } = params;
  const { vtxSignAppKey, vtxSignTimestamp, vtxSign } = signHeaders;

  return requestThirdParty({
    url: '/lajfApi/cloud/management/api/v101/login/loginByThird',
    method: 'POST',
    headers: {
      'vtxSignAppKey': vtxSignAppKey,
      'vtxSignTimestamp': vtxSignTimestamp,
      'vtxSign': vtxSign
    },
    data: {
      tenantCode,
      account,
      scope
    }
  });
}

/**
 * 生成签名时间戳
 * @returns {string} 当前时间戳
 */
export function generateTimestamp() {
  return Date.now().toString();
}

/**
 * 生成vtxSign签名
 * 签名规则：account${account}scope${scope}tenantCode${tenantCode}${vtxSignAppKey}${vtxSignAppSecret}${vtxSignTimestamp}
 * 然后进行SHA256加密
 * @param {Object} params - 登录参数
 * @param {string} params.account - 账户
 * @param {string} params.scope - 作用域
 * @param {string} params.tenantCode - 租户代码
 * @param {string} vtxSignAppKey - 应用密钥
 * @param {string} vtxSignAppSecret - 应用秘钥
 * @param {string} vtxSignTimestamp - 时间戳
 * @returns {string} 生成的签名
 */
export function generateVtxSign(params, vtxSignAppKey, vtxSignAppSecret, vtxSignTimestamp) {
  const { account, scope, tenantCode } = params;

  // 按照指定规则拼接字符串
  const paramStr = `account${account}scope${scope}tenantCode${tenantCode}${vtxSignAppKey}${vtxSignAppSecret}${vtxSignTimestamp}`;

  // 使用SHA256加密
  const vtxSign = CryptoJS.SHA256(paramStr).toString();

  return vtxSign;
}

/**
 * 第三方登录的便捷方法 - 自动生成时间戳和签名
 * @param {Object} params - 登录参数
 * @param {string} params.tenantCode - 租户代码
 * @param {string} params.account - 账户
 * @param {string} params.scope - 作用域
 * @param {Object} signConfig - 签名配置
 * @param {string} signConfig.appKey - 应用密钥
 * @param {string} signConfig.appSecret - 应用秘钥
 * @returns {Promise} 返回登录结果
 */
export function loginByThirdAuto(params, signConfig) {
  const timestamp = generateTimestamp();
  const vtxSign = generateVtxSign(params, signConfig.appKey, signConfig.appSecret, timestamp);

  return loginByThird(params, {
    vtxSignAppKey: signConfig.appKey,
    vtxSignTimestamp: timestamp,
    vtxSign: vtxSign
  });
}

/**
 * 第三方登录的完整便捷方法 - 只需要提供基本参数，自动处理所有签名逻辑
 * @param {Object} loginData - 完整的登录数据
 * @param {string} loginData.tenantCode - 租户代码
 * @param {string} loginData.account - 账户
 * @param {string} loginData.scope - 作用域
 * @param {string} loginData.appKey - 应用密钥
 * @param {string} loginData.appSecret - 应用秘钥
 * @returns {Promise} 返回登录结果
 */
export function loginByThirdComplete(loginData) {
  const { tenantCode, account, scope, appKey, appSecret } = loginData;

  const params = { tenantCode, account, scope };
  const signConfig = { appKey, appSecret };

  return loginByThirdAuto(params, signConfig);
}