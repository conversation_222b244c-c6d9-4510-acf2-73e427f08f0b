<template>
  <div>
    <CommonTitle text='视频列表'></CommonTitle>

    <!-- 添加标签页切换 -->
    <div class="tab-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="大华视频" name="dh">
          <div class='wrap-container'>
            <el-input
              placeholder="输入关键字进行过滤"
              v-model="filterText"
              prefix-icon="el-icon-search">
            </el-input>

            <el-tree
              class="filter-tree"
              :data="treeData"
              :props="defaultProps"
              :load="loadNode"
              lazy
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
              ref="tree">
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <span class="tree-icon" :class="[
                    data.isDevice ? 'icon-device' : (data.isLeaf === false ? 'icon-folder' : 'icon-video')
                  ]"></span>
                  <span class='el-tree-node__label' :title='node.label'>{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </el-tab-pane>

        <el-tab-pane label="海康威视视频" name="hk">
          <div class='wrap-container'>
            <el-input
              placeholder="输入关键字进行过滤海康威视视频"
              v-model="hkFilterText"
              prefix-icon="el-icon-search">
            </el-input>

            <el-tree
              class="filter-tree hk-tree"
              :data="hkTreeData"
              :props="hkDefaultProps"
              :load="loadHkNode"
              lazy
              :filter-node-method="filterHkNode"
              @node-click="handleHkNodeClick"
              ref="hkTree">
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <span class="tree-icon" :class="[
                    data.isDevice ? 'icon-hk-camera' :
                    data.isOrganization ? 'icon-hk-organization' :
                    (data.isLeaf === false ? 'icon-folder' : 'icon-video')
                  ]"></span>
                  <span class='el-tree-node__label' :title='node.label'>{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 加载中遮罩 -->
    <div v-if="loading" class="loading-mask">
      <i class="el-icon-loading"></i>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { TreeList, getVideoDeviceList } from '@/api/monitor/DWvideo'
import { HkTreeList } from '@/api/monitor/HKvideo'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      activeTab: 'dh', // 当前激活的标签页
      filterText: '',
      treeData: [], // 使用treeData作为树的数据源
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      loading: false,
      currentNode: null, // 当前选中的节点

      // 海康威视相关数据
      hkFilterText: '',
      hkTreeData: [], // 海康威视树数据
      hkDefaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      hkCurrentNode: null // 当前选中的海康威视节点
    }
  },
  computed: {
    // 从store获取海康威视视频历史记录
    ...mapGetters('monitorHistory', [
      'hkVideoHistory',
      'latestHkVideo',
      'hkVideoHistoryCount'
    ])
  },
  mounted() {
    // 初始加载根节点
    this.initTreeData()
    // 从store初始化历史记录（会从localStorage加载）
    this.initHistoryFromStorage()
  },
  methods: {
    // 映射store中的actions
    ...mapActions('monitorHistory', [
      'initHistoryFromStorage',
      'addHkVideoToHistory',
      'clearHkVideoHistory',
      'removeHkVideoFromHistory'
    ]),
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },

    // 初始化树数据
    async initTreeData() {
      this.loading = true
      try {
        // 加载根节点，parentId不需要传
        const res = await TreeList()
        if (res && res.data) {
          // 处理数据，标记文件夹节点(非叶子节点)
          this.treeData = this.processTreeData(res.data)
        }
      } catch (error) {
        console.error('获取树数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 处理树数据，标记是否为叶子节点
    processTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        // 根据isChild字段判断是否为文件夹节点
        const isLeaf = false
        // 是否是设备节点
        const isDevice = item.deviceType !== undefined

        return {
          ...item,
          isLeaf: isDevice ? true : isLeaf,  // 设备节点一定是叶子节点
          isDevice, // 标记是否为设备节点
          // 标准化name字段
          name: item.name || item.label || item.title || '未命名',
          // 保留原始ID和orgCode，用于请求子节点
          id: item.id || item.orgId || item.videoId,
          orgCode: item.orgCode || item.id
        }
      })
    },

    // 动态加载节点
    async loadNode(node, resolve) {
      // 如果是根节点，使用已加载的数据
      if (node.level === 0) {
        return resolve(this.treeData)
      }

      try {
        // 显示节点正在加载的状态
        this.$set(node.data, '_loading', true)

        // 如果节点是组织节点(isChild为false)且不是设备节点，则加载设备列表
        if (node.data.isChild === false && !node.data.isDevice) {
          await this.loadDeviceList(node, resolve)
        } else {
          // 否则加载子组织节点
          await this.loadOrgNodes(node, resolve)
        }

        // 移除加载状态
        this.$set(node.data, '_loading', false)
      } catch (error) {
        console.error('加载子节点失败:', error)
        this.$message.error('加载子节点失败')
        this.$set(node.data, '_loading', false)
        resolve([])
      }
    },

    // 加载组织节点
    async loadOrgNodes(node, resolve) {
      // 获取当前节点的ID作为parentId
      const parentId = node.data.id

      // 调用接口获取子节点
      const res = await TreeList({ parentId })

      if (res && res.data) {
        // 处理返回的数据
        const childNodes = this.processTreeData(res.data)
        resolve(childNodes)
      } else {
        resolve([])
      }
    },

    // 加载设备列表
    async loadDeviceList(node, resolve) {
      // 使用orgCode查询设备列表
      const orgCode = node.data.id

      // 调用设备列表接口
      const res = await getVideoDeviceList({ orgCode })

      if (res && res.data) {
        // 处理设备列表数据
        const deviceList = res.data.map(device => ({
          ...device,
          name: device.name || device.deviceName || '未命名设备',
          id: device.id || device.deviceId,
          orgCode: device.orgCode,
          isLeaf: true,
          isDevice: true,  // 标记为设备节点
          deviceType: device.deviceType || 'video'
        }))
        resolve(deviceList)
      } else {
        resolve([])
      }
    },

    // 节点点击事件
    async handleNodeClick(data, node) {
      console.log('点击的节点:', data)
      this.currentNode = data

      // 如果是设备节点，直接播放视频
      if (data.isDevice) {
        await this.playVideo(data)
        return
      }

      // 如果是叶子节点且不是设备节点，尝试加载设备列表
      if (data.isLeaf && !data.isDevice) {
        // 标记为非叶子节点以便于加载
        data.isLeaf = false
        // 展开节点，触发loadNode加载设备列表
        node.expanded = false // 先折叠确保重新加载
        this.$refs.tree.store.load(node, () => {
          node.expanded = true
        })
        return
      }

      // 如果是非叶子节点
      if (!data.isLeaf) {
        // 如果已加载过子节点，则展开/折叠
        if (node.loaded) {
          // 切换展开/折叠状态
          node.expanded = !node.expanded
        } else {
          // 展开节点，触发loadNode加载子节点
          this.$refs.tree.store.load(node, () => {
            // 加载完成后展开节点
            node.expanded = true
          })
        }
      }
    },

    // 播放视频
    async playVideo(data) {
      this.loading = true
      try {
        // 发送消息到视频播放页面，传递视频信息
        this.$bus.$emit('openVideo', {
          id: data.id,
          deviceCode: data.deviceCode,
          videoCode: data.channelId,
          title: data.name || '视频监控',

          name: data.name,
          deptName: data.deptName,
          cameraType: data.cameraType,
          tags: data.tags
        })
      } catch (error) {
        console.error('获取视频信息失败:', error)
        this.$message.error('获取视频信息失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新当前节点的子节点
    refreshCurrentNode() {
      if (!this.currentNode) return

      const node = this.$refs.tree.getNode(this.currentNode)
      if (node) {
        // 标记为未加载
        node.loaded = false
        // 清空子节点
        node.childNodes = []

        // 如果节点是展开的，重新加载子节点
        if (node.expanded) {
          this.$refs.tree.store.load(node, () => {
            // 重新展开节点
            node.expanded = true
          })
        }
      }
    },

    // ==================== 海康威视相关方法 ====================

    // 标签页切换事件
    handleTabClick(tab) {
      if (tab.name === 'hk' && this.hkTreeData.length === 0) {
        // 首次切换到海康威视标签页时，初始化数据
        this.initHkTreeData()
      }
    },

    // 初始化海康威视树数据
    async initHkTreeData() {
      this.loading = true
      try {
        // 加载海康威视根节点
        const res = await HkTreeList()
        if (res && res.data) {
          // 处理数据，标记文件夹节点(非叶子节点)
          this.hkTreeData = this.processHkTreeData(res.data)
        }
      } catch (error) {
        console.error('获取海康威视树数据失败:', error)
        this.$message.error('获取海康威视数据失败')
      } finally {
        this.loading = false
      }
    },

    // 处理海康威视树数据，标记是否为叶子节点
    processHkTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        // 海康威视数据结构判断
        // type == 2等于最后一级
        const isCamera = item.type == '2'
        const isOrganization = item.resourceType === 'organization' || item.nodeType === 'organization'

        return {
          ...item,
          isLeaf: isCamera, // 摄像头节点是叶子节点
          isDevice: isCamera, // 标记是否为设备节点
          isOrganization, // 标记是否为组织节点
          // 标准化name字段
          name: item.name || item.resourceName || item.cameraName || item.orgName || '未命名',
          // 保留原始ID，用于请求子节点
          id: item.id || item.resourceId || item.cameraIndexCode || item.orgId,
          // 海康威视特有字段
          cameraIndexCode: item.id,
          resourceType: item.resourceType,
          nodeType: item.nodeType,
          parentId: item.parentId
        }
      })
    },

    // 海康威视动态加载节点
    async loadHkNode(node, resolve) {
      // 如果是根节点，使用已加载的数据
      if (node.level === 0) {
        return resolve(this.hkTreeData)
      }

      try {
        // 显示节点正在加载的状态
        this.$set(node.data, '_loading', true)

        // 海康威视树结构：如果是组织节点且不是设备节点，则加载子节点
        if (node.data.isOrganization || (!node.data.isDevice && !node.data.isLeaf)) {
          await this.loadHkChildNodes(node, resolve)
        } else {
          // 如果是叶子节点，不需要加载子节点
          resolve([])
        }

        // 移除加载状态
        this.$set(node.data, '_loading', false)
      } catch (error) {
        console.error('加载海康威视子节点失败:', error)
        this.$message.error('加载海康威视子节点失败')
        this.$set(node.data, '_loading', false)
        resolve([])
      }
    },

    // 加载海康威视子节点
    async loadHkChildNodes(node, resolve) {
      // 获取当前节点的ID作为parentId
      const parentId = node.data.id

      // 调用海康威视接口获取子节点
      const res = await HkTreeList({ parentId })

      if (res && res.data) {
        // 处理返回的数据
        const childNodes = this.processHkTreeData(res.data)
        resolve(childNodes)
      } else {
        resolve([])
      }
    },

    // 海康威视节点过滤
    filterHkNode(value, data) {
      if (!value) return true;
      return data.name.toLowerCase().indexOf(value.toLowerCase()) !== -1;
    },

    // 海康威视节点点击事件
    async handleHkNodeClick(data, node) {
      console.log('点击的海康威视节点:', data)
      this.hkCurrentNode = data

      // 如果是摄像头设备节点，直接播放视频并保存到store
      if (data.isDevice && data.cameraIndexCode) {
        await this.playHkVideo(data)
        // 通过store保存到历史记录（会自动同步到localStorage）
        this.addHkVideoToHistory(data)
        return
      }

      // 如果是组织节点或非叶子节点
      if (data.isOrganization || !data.isLeaf) {
        // 如果已加载过子节点，则展开/折叠
        if (node.loaded) {
          // 切换展开/折叠状态
          node.expanded = !node.expanded
        } else {
          // 展开节点，触发loadHkNode加载子节点
          this.$refs.hkTree.store.load(node, () => {
            // 加载完成后展开节点
            node.expanded = true
          })
        }
      }
    },

    // 播放海康威视视频
    async playHkVideo(data) {
      this.loading = true
      try {
        // 发送消息到视频播放页面，传递海康威视视频信息
        this.$bus.$emit('openHkVideo', {
          id: data.id,
          cameraIndexCode: data.cameraIndexCode, // 海康威视摄像头编码
          resourceId: data.resourceId,
          title: data.name || '海康威视视频监控',
          name: data.name,
          resourceType: data.resourceType,
          nodeType: data.nodeType,
          parentId: data.parentId
        })

        console.log('播放海康威视视频:', {
          cameraIndexCode: data.cameraIndexCode,
          name: data.name
        })
      } catch (error) {
        console.error('获取海康威视视频信息失败:', error)
        this.$message.error('获取海康威视视频信息失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新海康威视当前节点的子节点
    refreshHkCurrentNode() {
      if (!this.hkCurrentNode) return

      const node = this.$refs.hkTree.getNode(this.hkCurrentNode)
      if (node) {
        // 标记为未加载
        node.loaded = false
        // 清空子节点
        node.childNodes = []

        // 如果节点是展开的，重新加载子节点
        if (node.expanded) {
          this.$refs.hkTree.store.load(node, () => {
            // 重新展开节点
            node.expanded = true
          })
        }
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    hkFilterText(val) {
      this.$refs.hkTree && this.$refs.hkTree.filter(val);
    }
  },
}
</script>

<style scoped lang='less'>
.tree-icon {
  display: inline-block;
  margin-right: 8px;
}
.icon-folder {
  width: 28px;
  height: 28px;
  background: url("@/assets/index/folder.png") no-repeat;
  background-size: 100% 100%;
  margin: 0 20px 0 10px;
}
.icon-video {
  width: 28px;
  height: 28px;
  background: url("@/assets/index/video.png") no-repeat;
  background-size: 100% 100%;
  margin: 0 20px 0 10px;
}
.icon-device {
  width: 28px;
  height: 28px;
  background: url("@/assets/index/video.png") no-repeat;
  background-size: 100% 100%;
  margin: 0 20px 0 10px;
}

// 海康威视图标样式
.icon-hk-camera {
  width: 28px;
  height: 28px;
  background: url("@/assets/index/video.png") no-repeat;
  background-size: 100% 100%;
  margin: 0 20px 0 10px;
}

.icon-hk-organization {
  width: 28px;
  height: 28px;
  background: url("@/assets/index/folder.png") no-repeat;
  background-size: 100% 100%;
  margin: 0 20px 0 10px;
}
.wrap-container {
  width: 100%;
  height: 1789px;
  /deep/ .el-input__inner {
    width: 910px;
    height: 80px;
    background: rgba(255,255,255,0.16);
    border-radius: 8px;
    border: unset;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 28px;
    color: #FFFFFF;
    line-height: 80px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 43px 0 43px 93px
  }
  /deep/ .el-input__prefix  {
    left: 40px;
    top: 62px;
    .el-icon-search:before {
      content: "\e778";
      font-size: 27px;
    }
  }
  /deep/ .el-tree {
    background: transparent;
    margin: 0 40px 0 40px;
    height: 1600px;
    overflow-y: scroll;
    .el-tree-node {
      .el-tree-node__content {
        height: 52px;
        .el-tree-node__expand-icon {
          font-size: 24px;
        }
        .el-tree-node__label {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 36px;
          color: #A8D6FF;
          line-height: 52px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .el-tree-node__content:hover {
        background: rgba(255,255,255,0.16) !important;
      }
      .el-tree-node__content:active {
        background: rgba(255,255,255,0.16) !important;
      }
      /* 修改选中节点的背景色 */
      .el-tree-node.is-current > .el-tree-node__content {
        // 选中时的背景色
        background-color: rgba(255,255,255,0.16) !important;
      }
    }
  }
}

:deep(.el-tree) {
  // 移除默认的focus样式
  .el-tree-node:focus > .el-tree-node__content {
    background-color: transparent !important;
  }

  // 移除默认的点击样式
  .el-tree-node__content:focus {
    background-color: transparent !important;
  }

  // 统一hover和active状态的背景色
  .el-tree-node__content {
    &:hover, &:active {
      background-color: rgba(255, 255, 255, 0.16) !important;
    }
  }

  // 确保选中状态的背景色正确
  .el-tree-node.is-current > .el-tree-node__content {
    background-color: rgba(255, 255, 255, 0.16) !important;
  }
}

// 移除默认的点击outline
:deep(.el-tree-node__content:focus-visible) {
  outline: none;
}

:deep(.el-tree-node__content) {
  height: 40px;

  &:hover {
    background-color: rgba(255,255,255,0.16);
  }
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  // 选中时的背景色
  background-color: rgba(255,255,255,0.16);

  // 如果需要圆角效果
  border-radius: 4px;
}

// 如果需要调整树节点之间的间距
:deep(.el-tree-node__children) {
  margin-top: 4px;
}

// 标签页容器样式
.tab-container {
  /deep/ .el-tabs__header {
    margin: 0 0 20px 0;

    .el-tabs__nav-wrap {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      margin: 0 40px;
    }

    .el-tabs__item {
      color: #A8D6FF;
      font-size: 24px;
      height: 60px;
      line-height: 60px;

      &.is-active {
        color: #FFFFFF;
        background: rgba(255, 255, 255, 0.16);
        border-radius: 8px;
      }

      &:hover {
        color: #FFFFFF;
      }
    }

    .el-tabs__active-bar {
      display: none; // 隐藏默认的激活条
    }
  }
}

// 摄像头编码样式
.camera-code {
  font-size: 20px;
  color: #999;
  margin-left: 8px;
  opacity: 0.8;
}

// 自定义树节点样式
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;

  .el-tree-node__label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 海康威视树特殊样式
.hk-tree {
  .custom-tree-node {
    .camera-code {
      font-size: 18px;
      color: #66B3FF;
      background: rgba(102, 179, 255, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      margin-left: 8px;
    }
  }
}

// 加载遮罩样式
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  i {
    font-size: 40px;
    color: #fff;
  }

  p {
    margin-top: 10px;
    color: #fff;
    font-size: 16px;
  }
}
</style>