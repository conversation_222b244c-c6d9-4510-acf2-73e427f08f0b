# TinyPNG 图片压缩工具使用文档

## 📖 简介

TinyPNG 图片压缩工具是一个基于 TinyPNG API 的 Python 脚本，可以高质量地压缩 PNG、JPG、JPEG 和 WebP 格式的图片文件。相比本地压缩工具，TinyPNG 使用智能有损压缩技术，在保持视觉质量的同时显著减小文件大小。

## 🚀 快速开始

### 1. 获取 TinyPNG API Key

1. 访问 [TinyPNG 开发者页面](https://tinypng.com/developers)
2. 输入你的姓名和邮箱地址
3. 点击 "Get your API key" 获取免费 API Key
4. 免费账户每月可压缩 500 张图片

### 2. 安装依赖

```bash
pnpm run setup-image-compression
```

### 3. 开始压缩

**基本用法（会提示输入 API Key）：**
```bash
pnpm run tinypng-compress
```

## 📋 可用命令

### NPM 脚本命令

| 命令 | 说明 |
|------|------|
| `pnpm run tinypng-compress` | 压缩 src/assets 目录下所有支持的图片 |
| `pnpm run tinypng-compress:min-1mb` | 只压缩大于 1MB 的图片文件 |
| `pnpm run tinypng-compress:min-2mb` | 只压缩大于 2MB 的图片文件 |

### 直接使用 Python 脚本

```bash
# 基本用法
python scripts/tinypng_compress.py

# 指定 API Key
python scripts/tinypng_compress.py --api-key YOUR_API_KEY

# 自定义目录和参数
python scripts/tinypng_compress.py --dir src/images --max-size 0.5 --delay 1.0

# 显示帮助信息
python scripts/tinypng_compress.py --help
```

## ⚙️ 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--dir` | 字符串 | `src/assets` | 要压缩的目标目录 |
| `--max-size` | 浮点数 | 无限制 | 最小文件大小限制（MB），小于此大小的文件将被跳过 |
| `--delay` | 浮点数 | `0.5` | 请求间隔时间（秒），避免 API 限流 |
| `--api-key` | 字符串 | 无 | TinyPNG API Key，如果不提供将提示输入 |

## 📊 支持的图片格式

- **PNG** - 完全支持，保持透明度
- **JPG/JPEG** - 完全支持
- **WebP** - 完全支持

## 🔧 使用示例

### 示例 1：压缩所有图片
```bash
pnpm run tinypng-compress
```
输入 API Key 后，脚本会压缩 `src/assets` 目录下所有支持格式的图片。

### 示例 2：只压缩大文件
```bash
pnpm run tinypng-compress:min-2mb
```
只压缩大于 2MB 的图片文件，小文件会被跳过。

### 示例 3：自定义目录和参数
```bash
python scripts/tinypng_compress.py --dir public/images --max-size 1.0 --delay 1.0 --api-key abc123
```
压缩 `public/images` 目录下大于 1MB 的图片，请求间隔 1 秒。

### 示例 4：批量处理多个目录
```bash
# 压缩资源目录
python scripts/tinypng_compress.py --dir src/assets --api-key abc123

# 压缩公共图片目录
python scripts/tinypng_compress.py --dir public/images --api-key abc123
```

## 📈 输出信息说明

脚本运行时会显示详细的压缩信息：

```
开始扫描目录: src/assets
支持格式: .png, .jpg, .jpeg, .webp
大小限制: 1.0MB
请求间隔: 0.5秒
------------------------------------------------------------
找到 5 个需要压缩的文件
------------------------------------------------------------
[1/5] 正在压缩: src/assets/logo.png (2.34MB)
  ✓ 压缩成功!
  原始大小: 2.34MB
  压缩后: 0.89MB
  节省: 1.45MB (62.0%)
[2/5] 正在压缩: src/assets/banner.jpg (1.56MB)
  ✓ 压缩成功!
  原始大小: 1.56MB
  压缩后: 0.78MB
  节省: 0.78MB (50.0%)
------------------------------------------------------------
压缩完成!
总文件数: 5
成功压缩: 5
失败数量: 0
总共节省: 3.45MB
```

## ⚠️ 注意事项

### API 限制
- **免费账户**：每月 500 张图片
- **付费账户**：根据套餐不同有不同限制
- 建议设置合理的 `--delay` 参数避免触发限流

### 文件安全
- 脚本会直接覆盖原文件
- 建议在压缩前备份重要图片
- 可以先在测试目录中验证效果

### 网络要求
- 需要稳定的网络连接
- 图片会上传到 TinyPNG 服务器处理
- 大文件上传可能需要较长时间

## 🔍 故障排除

### 常见错误及解决方案

**1. API Key 无效**
```
✗ API Key 无效
```
- 检查 API Key 是否正确
- 确认 API Key 是否已激活

**2. API 调用次数超限**
```
✗ API 调用次数超限
```
- 检查当月使用量是否超过限制
- 考虑升级到付费套餐

**3. 网络连接错误**
```
✗ 网络错误: Connection timeout
```
- 检查网络连接
- 尝试增加 `--delay` 参数
- 检查防火墙设置

**4. 图片格式不支持**
```
✗ 图片格式不支持或文件损坏
```
- 确认图片格式是否为 PNG/JPG/WebP
- 检查图片文件是否损坏

## 🆚 与本地压缩的对比

| 特性 | TinyPNG 压缩 | 本地压缩 |
|------|-------------|----------|
| 压缩质量 | 智能有损压缩，质量更高 | 基于质量参数的标准压缩 |
| 压缩率 | 通常更高（30-70%） | 中等（20-50%） |
| 处理速度 | 受网络影响，较慢 | 快速 |
| 使用限制 | 有 API 调用次数限制 | 无限制 |
| 隐私性 | 图片上传到第三方服务 | 完全本地处理 |
| 成本 | 免费额度有限，超出需付费 | 完全免费 |

## 📝 最佳实践

1. **测试先行**：在正式使用前，先在小范围测试压缩效果
2. **备份重要文件**：压缩前备份原始图片文件
3. **合理设置参数**：根据需求设置 `--max-size` 参数，避免压缩小文件
4. **监控 API 使用量**：定期检查 API 使用情况，避免超限
5. **网络稳定性**：确保网络连接稳定，避免压缩过程中断

## 🔗 相关链接

- [TinyPNG 官网](https://tinypng.com/)
- [TinyPNG API 文档](https://tinypng.com/developers/reference)
- [TinyPNG 定价](https://tinypng.com/developers/subscription)
