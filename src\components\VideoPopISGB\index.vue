<!--执法仪弹窗国标7.0-->
<template>
  <ygfDialog :visible='visible' width='1500px' style='margin-top: 100px;'>
    <div class="zfry-video-container">
      <div class="rwgz-tc">
        <div class="rw-title flex-between">
          <div class="fs-44 font-syBold dialogTitle">{{ title }}</div>
          <div class="close" @click="closeVideo"></div>
        </div>
        <video
          ref="video"
          controls="controls"
          autoplay="autoplay"
          muted="false"
          style="
            width: 94%;
            height: 90%;
            position: absolute;
            top: 75px;
            left: 50px;
          "
        ></video>
        <div
          class="control-panel"
          style="
            z-index: 1000;
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            gap: 10px;
          "
        >
          <button
            class="btn"
            style="
              background-color: #1890ff;
              color: white;
              border: none;
              padding: 8px 15px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            "
            @click="startVideo"
          >
            播放视频
          </button>
          <button
            class="btn"
            style="
              background-color: #1890ff;
              color: white;
              border: none;
              padding: 8px 15px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            "
            @click="toggleAudio"
          >
            {{ isAudio ? '关闭对讲' : '开启对讲' }}
          </button>
          <button
            class="btn"
            style="
              background-color: #1890ff;
              color: white;
              border: none;
              padding: 8px 15px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            "
            @click="stopAll"
          >
            停止
          </button>
        </div>
        <div
          class="status"
          ref="statusText"
          style="
            position: absolute;
            top: 60px;
            left: 20px;
            color: #fff;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
          "
        ></div>
        <div
          class="loading"
          ref="loading"
          style="
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 18px;
            display: none;
          "
        >
          正在加载，请稍候...
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { hex_md5 } from './md5.js'
import {
  getkey,
  online,
  get_info,
  login,
  getDeviceGB,
  startLiveGB,
  stopLiveGB,
  startAudioGB,
  stopAudioGB,
} from '@/api/zfjlyApi/gb'
export default {
  name: "index",
  components: {
    ygfDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    baseURL: {
      type: String,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    pwd: {
      type: String,
      required: true
    },
    videoCode: {
      type: String,
      default: "T0C2675"
    },
    title: {
      type: String,
      default: "执法记录仪"
    },
    type: {
      type: String,
      default: "openVideoTest" // 可选值: openVideoTest, ddhjVideoTest
    }
  },
  data() {
    return {
      player: null,
      isAudio: false,
      num: 0,
      recorders: null,
      recordersPlatform: null,
      ws: null,
      timerId: null,
      dataInfo_set: [],
      form: {
        hostbodyArr: null,
        hostbody: null
      },
      dataInfo_hostbody: null,
      wsConnected: false,
      webRtcUrl: null,
      currentSn: null,
      currentHostbody: null,
      wsChannelId: null,
      sn: null,
      isStartAudio: 0,
      isVolumeOn: false,
      wsUrl: null
    };
  },
  created() {
    // 初始化时只执行一次，不再放在这里
  },
  mounted() {
    if (this.visible) {
      this.init();
    }
  },
  beforeDestroy() {
    this.closeAll();
  },
  methods: {
    async init() {
      this.showLoading(true);
      this.showStatus("正在连接执法记录仪平台...");

      this.currentHostbody = this.videoCode;

      // 登录系统
      const loginSuccess = await this.loginZFJLY();
      if (loginSuccess) {
        this.showStatus("已连接执法记录仪平台");
        this.wsConnected = true;

        // 获取设备信息
        await this.getDeviceInfo();
        this.showLoading(false);

        if (this.type === "openVideoTest") {
          this.startVideo();
        } else if (this.type === "ddhjVideoTest") {
          this.startVideo();
          setTimeout(() => {
            this.startAudio();
          }, 2000);
        }
      } else {
        this.showStatus("连接执法记录仪平台失败");
        this.showLoading(false);
      }
    },

    closeVideo() {
      // 停止所有流和释放资源
      this.stopAll();
    },

    // 参数签名方法 - 参考VideoPopNOGB实现
    hex_md5(str) {
      return hex_md5(str);
    },
    param_up(param_arr) {
      var keys = Object.keys(param_arr).sort();
      var string = "";
      for (var i = 0; i < keys.length; i++) {
        var k = keys[i];
        string += k + "=" + param_arr[k] + ";";
      }
      string += this.hex_md5("Pe2695jingyi");
      let str_encode = encodeURIComponent(string);
      param_arr.pe_signals = this.hex_md5(str_encode);
      return JSON.stringify(param_arr);
    },
    extendSignal(target) {
      let keys = Object.keys(target),
        arr = [],
        solt = "Pe2695jingyi",
        str,
        pe_signals;
      keys.sort();
      keys.forEach((key) => {
        const value = JSON.stringify(target[key]);
        arr.push(`${key}=${value}`);
      });
      str = arr.join(";") + this.hex_md5(solt);
      str = encodeURIComponent(str);
      pe_signals = this.hex_md5(str);
      target.pe_signals = pe_signals;
      return target;
    },
    // 登录执法记录仪平台 - 参考zfjly.js实现
    async loginZFJLY() {
      return new Promise((resolve) => {
        this.getLoginBaseInfo("").then((res) => {
          const send = {
            username: this.username,
            pwd: this.pwd,
            token: res.data,
          };


          this.login(send).then((el) => {
            console.log(el,"zfyLogin")
            if (el.data.code === 200) {
              this.heartbeat().then(() => {
                this.getUserInformation().then((e) => {
                  try {
                    this.wsUrl = e.data.data.wsurl;
                    this.createWebSocket(e.data.data.wsurl, e.data.data);
                    resolve(true);
                  } catch (error) {
                    console.log("WebSocket创建失败:", error);
                    resolve(false);
                  }
                });
              });
            } else {
              resolve(false);
            }
          }).catch(error => {
            console.error("登录请求失败:", error);
            resolve(false);
          });
        }).catch(error => {
          console.error("获取登录基础信息失败:", error);
          resolve(false);
        });
      });
    },

    // 创建WebSocket连接 - 参考zfjly.js实现
    createWebSocket(wsUrl, userData) {
      const _this = this;
      try {
        this.ws = new WebSocket(wsUrl);
      } catch (error) {
        console.log("WebSocket创建失败:", error);
        return;
      }

      const data1 = {
        logincode: userData.logincode,
        username: userData.username,
        scode: userData.scode,
        cate: userData.auth_cate,
      };

      const psd = {
        command: "client_login",
        data: JSON.stringify(data1),
      };

      this.ws.onopen = function () {
        console.log("WebSocket已连接，发送登录信息");
        _this.ws.send(JSON.stringify(psd));
      };

      this.ws.onerror = function (e) {
        console.warn("WebSocket连接出错:", e);
        _this.ws.close();
        _this.ws = null;
      };

      this.ws.onclose = function () {
        console.log("WebSocket已断开连接");
      };

      this.ws.onmessage = function (event) {
        console.log("WebSocket收到消息:", event.data);
        try {
          const data = JSON.parse(event.data);

          // 处理site_data信息 - 参考zfjly.js的处理方式
          if (data.site_data) {
            const dataInfo = data.site_data;

            // 处理开始播放视频事件
            if (dataInfo.start_live) {
              const ws = "ws://" + dataInfo.start_live.wsInfo.wsIp + ":" + dataInfo.start_live.wsInfo.wsPort;
              const viewId = dataInfo.start_live.wsInfo.wsViewId;
              _this.pullFlow_vms2(ws, viewId);
            }

            // 处理开始音频事件
            if (dataInfo.start_audio) {
              _this.isAudio = true;
              const wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
              _this.wsChannelId = dataInfo.start_audio.wsInfo.wsChannelId;
              _this.currentSn = dataInfo.start_audio.wsInfo.sn;
              _this.voice_pull_vms2(wss, _this.wsChannelId);
            }
          }

          // 处理startLive接口调用后的WebSocket回调消息 - 参考zfjly.js实现
          if (data.start_live) {
            console.log("收到startLive WebSocket回调消息:", data.start_live);

            if (data.start_live.code == 200) {
              console.log("视频流请求成功，开始处理视频流");

              // 保存流信息
              if (data.start_live.sn) {
                _this.currentSn = data.start_live.sn;
                console.log("保存视频流SN:", _this.currentSn);
              }

              // 处理视频流地址
              if (data.start_live.play_info) {
                const playInfo = data.start_live.play_info;
                console.log("视频流播放信息:", playInfo);

                // 优先使用WebRTC流地址
                if (playInfo.webrtc_url) {
                  console.log("使用WebRTC播放视频流:", playInfo.webrtc_url);
                  _this.pullFlow_vms2("webrtc", playInfo.webrtc_url);
                }
                // 备选使用RTSP流地址
                else if (playInfo.rtsp_url) {
                  console.log("使用RTSP播放视频流:", playInfo.rtsp_url);
                  _this.pullFlow_vms2("rtsp", playInfo.rtsp_url);
                }
                // 如果有WebSocket信息，使用WebSocket方式
                else if (data.start_live.wsInfo) {
                  const ws = "ws://" + data.start_live.wsInfo.wsIp + ":" + data.start_live.wsInfo.wsPort;
                  const viewId = data.start_live.wsInfo.wsViewId;
                  console.log("使用WebSocket播放视频流:", ws, viewId);
                  _this.pullFlow_vms2(ws, viewId);
                }
              }
              // 兼容旧格式的wsInfo
              else if (data.start_live.wsInfo) {
                const ws = "ws://" + data.start_live.wsInfo.wsIp + ":" + data.start_live.wsInfo.wsPort;
                const viewId = data.start_live.wsInfo.wsViewId;
                console.log("使用WebSocket播放视频流(兼容模式):", ws, viewId);
                _this.pullFlow_vms2(ws, viewId);
              }
            } else {
              console.log("视频流请求失败:", data.start_live.msg || "未知错误");
              _this.showStatus("视频流请求失败: " + (data.start_live.msg || "未知错误"));
              _this.showLoading(false);
            }
          }

          // 处理startAudio接口调用后的WebSocket回调消息 - 参考zfjly.js实现
          if (data.start_audio) {
            console.log("收到startAudio WebSocket回调消息:", data.start_audio);

            if (data.start_audio.code == 200) {
              console.log("音频对讲请求成功，开始处理音频流");

              // 保存音频流信息
              if (data.start_audio.sn) {
                _this.currentSn = data.start_audio.sn;
                console.log("保存音频流SN:", _this.currentSn);
              }

              // 处理音频流地址
              if (data.start_audio.play_info) {
                const playInfo = data.start_audio.play_info;
                console.log("音频流播放信息:", playInfo);

                // 使用WebRTC推流地址
                if (playInfo.webrtc_push_url) {
                  console.log("使用WebRTC进行音频对讲:", playInfo.webrtc_push_url);
                  _this.voice_pull_vms2("webrtc", playInfo.webrtc_push_url);
                }
                // 备选使用WebSocket方式
                else if (data.start_audio.wsInfo) {
                  const wss = `ws://${data.start_audio.wsInfo.wsIp}:${data.start_audio.wsInfo.wsPort}`;
                  _this.wsChannelId = data.start_audio.wsInfo.wsChannelId;
                  console.log("使用WebSocket进行音频对讲:", wss, _this.wsChannelId);
                  _this.voice_pull_vms2(wss, _this.wsChannelId);
                }
              }
              // 兼容旧格式的wsInfo
              else if (data.start_audio.wsInfo) {
                const wss = `ws://${data.start_audio.wsInfo.wsIp}:${data.start_audio.wsInfo.wsPort}`;
                _this.wsChannelId = data.start_audio.wsInfo.wsChannelId;
                console.log("使用WebSocket进行音频对讲(兼容模式):", wss, _this.wsChannelId);
                _this.voice_pull_vms2(wss, _this.wsChannelId);
              }
            } else {
              console.log("音频对讲请求失败:", data.start_audio.msg || "未知错误");
              _this.showStatus("音频对讲请求失败: " + (data.start_audio.msg || "未知错误"));
            }
          }
        } catch (error) {
          console.error("解析WebSocket消息错误:", error);
        }
      };
    },
    // 登录方法 - 使用封装的API
    login(param) {
      const base64 = {
        encode: function (str) {
          return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_, hex) {
            return String.fromCharCode('0x' + hex);
          }));
        }
      };

      let { username, pwd, token, captcha_code } = param;
      let password = hex_md5(pwd);
      let login_if = base64.encode(JSON.stringify({ username, password }));
      let data = this.param_up({ login_info: login_if, token, captcha_code, 'withCredentials': true });
      return login(this.baseURL, data);
    },

    // 获取登录基础信息 - 使用封装的API
    getLoginBaseInfo(key) {
      return getkey(this.baseURL, key);
    },
    getUserInformation() {
      return get_info(this.baseURL);
    },
    heartbeat() {
      return new Promise((resolve) => {
        this.heart();
        this.timerId = setInterval(this.heart, 20000);
        resolve();
      });
    },
    heart() {
      this.online().then(() => {
        console.log("心跳请求成功");
      }).catch(err => {
        console.error("心跳请求失败:", err);
      });
    },
    online() {
      return online(this.baseURL);
    },
    async getDeviceInfo() {
      try {
        const res = await this.unitEquipTreeGB("1001", "", [1, 2, 3, 4, 5]);
        var lineon = [];
        if (res.data && res.data.data && res.data.data.gblineon) {
          res.data.data.gblineon.forEach((item) => {
            lineon.push(item.hostbody);
          });
          this.pulldata = res.data.gblineon;
        }
        this.dataInfo_hostbody = lineon.toString();
        this.form.hostbodyArr = this.dataInfo_hostbody;
      } catch (error) {
        console.error("获取设备信息失败:", error);
      }
    },
    unitEquipTreeGB(unit = '', key = '', recorder_type_arr = []) {
      let data = { unit, key, recorder_type_arr };
      this.extendSignal(data);
      return getDeviceGB(this.baseURL, data);
    },

    startLiveVideo(data) {
      this.extendSignal(data);
      return startLiveGB(this.baseURL, data);
    },
    startLiveAudio(data) {
      this.extendSignal(data);
      return startAudioGB(this.baseURL, data);
    },
    // 开始音频对讲 - 参考video.html实现
    async startAudio() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在初始化麦克风...");

      try {
        // 初始化麦克风
        await this.initMicrophone();

        this.showStatus("正在建立对讲连接...");

        // 只调用接口，不处理音频流
        // 音频流处理将在WebSocket回调中进行
        const send = {
          hostbody_arr: [this.currentHostbody]
        };

        const res = await this.startLiveAudio(send);
        console.log("startLiveAudio接口调用结果:", res);

        if (res.data.code === 200) {
          this.showStatus("音频对讲请求成功，等待WebSocket回调处理音频流...");
          this.showLoading(false);

          // 注意：不在这里处理音频流，等待WebSocket消息回调中的voice_pull_vms2处理
        } else {
          this.showStatus("设备不在线或不支持对讲");
          this.showLoading(false);
        }
      } catch (err) {
        console.error("对讲初始化失败:", err);
        this.showStatus("对讲初始化失败");
        this.showLoading(false);
      }
    },
    initMicrophone() {
      return new Promise((resolve, reject) => {
        // 导入音频处理模块
        import('./JY-chromePlayer.min.js').then(({ HZRecorder_pcm_push, HZRecorder_pcm }) => {
          navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

          if (!navigator.getUserMedia) {
            this.$message({
              message: "浏览器不支持音频输入",
              type: "warning"
            });
            reject("浏览器不支持音频输入");
            return;
          }

          navigator.getUserMedia(
            { audio: true },
            (stream) => {
              this.recordersPlatform = new HZRecorder_pcm_push(stream, {});
              this.recorders = new HZRecorder_pcm(stream, {});
              resolve();
            },
            (error) => {
              console.error("麦克风访问失败:", error);
              reject("麦克风访问失败: " + (error.name || error.message || "未知错误"));
            }
          );
        }).catch(err => {
          reject(err);
        });
      });
    },

    stopLive(data) {
      this.extendSignal(data);
      return stopLiveGB(this.baseURL, data);
    },
    stopAudio() {
      if (!this.isAudio) return;

      this.showStatus("正在关闭对讲...");

      if (this.recordersPlatform) {
        this.recordersPlatform.closeWebsocket();
      }

      // 调用停止音频接口
      if (this.currentHostbody) {
        const stopData = {
          hostbody_arr: [this.currentHostbody]
        };
        this.extendSignal(stopData);
        stopAudioGB(this.baseURL, stopData).then(() => {
          console.log("音频对讲接口已停止");
        }).catch(err => {
          console.error("停止音频对讲接口失败:", err);
        });
      }

      this.isAudio = false;
      this.showStatus("对讲已关闭");
    },
    // 开始视频播放 - 参考video.html实现
    async startVideo() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在请求视频流...");

      try {
        // 只调用startLive接口，不处理视频流
        // 视频流处理将在WebSocket回调中进行
        const res = await this.startLive(this.currentHostbody);
        console.log("startLive接口调用结果:", res);
        this.showLoading(false);

        if (res.data.code == 200) {
          // 保存sn值，用于停止视频流时使用
          if (res.data.data && res.data.data[0] && res.data.data[0].sn) {
            this.currentSn = res.data.data[0].sn;
            console.log("获取到视频流SN:", this.currentSn);
          }

          this.showStatus("视频流请求成功，等待WebSocket回调处理视频流...");

          // 注意：不在这里处理视频流，等待WebSocket消息回调中的pullFlow_vms2处理
        } else {
          this.showStatus("无法连接设备: " + (res.data.msg || "未知错误"));
        }
      } catch (err) {
        console.error("视频流请求失败:", err);
        this.showStatus("视频流请求失败: " + err);
        this.showLoading(false);
      }
    },

    // 开始拉流指定设备的视频 - 使用封装的API
    startLive(hostbody) {
      const send = {
        hostbody_arr: Array.isArray(hostbody) ? hostbody : [hostbody || "T0C0223"],
      };
      this.extendSignal(send);
      return startLiveGB(this.baseURL, send);
    },

    // 创建WebRTC播放器并播放视频流 - 参考zfjly.js实现
    createWebRTCPlayer(videoElement, webRtcUrl) {
      if (!videoElement) {
        console.error("播放器元素不存在");
        return Promise.reject("播放器元素不存在");
      }

      return new Promise((resolve, reject) => {
        try {
          // 创建WebRTC播放器配置
          const webrtcConfig = {
            element: videoElement,
            debug: true,
            zlmsdpUrl: webRtcUrl,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 1280, h: 720 },
            usedatachannel: false
          };

          // 创建WebRTC播放器实例
          const webrtcPlayer = new Webrtc(webrtcConfig);

          // 开始播放
          webrtcPlayer.start_play().then(stream => {
            console.log("WebRTC播放成功:", stream);
            resolve({
              player: webrtcPlayer,
              stream: stream,
              type: true
            });
          }).catch(error => {
            console.error("WebRTC播放失败:", error);
            reject(error);
          });
        } catch (error) {
          console.error("创建WebRTC播放器失败:", error);
          reject(error);
        }
      });
    },

    // 拉流处理函数 - 参考video.html实现
    pullFlow_vms2(streamType, streamUrl) {
      console.log("pullFlow_vms2", streamType, streamUrl);

      if (streamType === "webrtc") {
        // 使用WebRTC播放
        console.log("使用WebRTC播放视频流:", streamUrl);
        this.createWebRTCPlayer(this.$refs.video, streamUrl).then((result) => {
          console.log("WebRTC拉流结果：", result);

          if (result && result.type == true) {
            this.$refs.video.classList.add("active");
            this.player = result.player;

            // 保存来自WebSocket的sn值
            if (this.currentSn) {
              console.log("WebSocket消息触发的视频流，保存SN:", this.currentSn);
            }

            this.showStatus("视频播放中");
          } else {
            this.showStatus("视频拉流失败");
          }
        }).catch(error => {
          console.error("WebRTC拉流过程中发生错误：", error);
          this.showStatus("拉流失败，请检查连接");
        });
        return;
      } else if (streamType === "rtsp") {
        // 处理RTSP流（可能需要转换为WebRTC或其他方式）
        console.log("处理RTSP视频流:", streamUrl);
        this.showStatus("RTSP流暂不支持直接播放");
        return;
      }

      // 兼容旧的WebSocket方式调用 pullFlow_vms2(ws, viewId)
      const ws = streamType;
      const viewId = streamUrl;

      // 检查是否有Wsplayer类可用
      if (typeof Wsplayer !== 'undefined') {
        try {
          // 导入播放器模块
          import('./JY-chromePlayer.min.js').then(({ Wsplayer }) => {
            this.player = new Wsplayer(ws, viewId, this.$refs.video, "isGB");
            this.player.openws().then((obj) => {
              console.log("WebSocket拉流结果:", obj);
              if (obj.type == true) {
                // 拉流成功
                this.$refs.video.classList.add("active");
                this.showStatus("视频播放中");
              } else {
                // 拉流失败
                this.showStatus("视频拉流失败");
                console.error("WebSocket拉流失败");
              }
            }).catch(error => {
              console.error("WebSocket拉流异常:", error);
              this.showStatus("视频拉流异常");
            });
          }).catch(error => {
            console.error("导入播放器模块失败:", error);
            this.showStatus("播放器模块加载失败");
          });
        } catch (error) {
          console.error("创建Wsplayer失败:", error);
          this.showStatus("播放器初始化失败");
        }
      } else {
        console.warn("Wsplayer类未定义，无法使用WebSocket方式播放");
        this.showStatus("播放器不可用");
      }
    },

    // 音频流处理 - 参考video.html实现
    voice_pull_vms2(streamType, streamUrl) {
      console.log("voice_pull_vms2", streamType, streamUrl);

      if (streamType === "webrtc") {
        // 使用WebRTC进行音频对讲
        console.log("使用WebRTC进行音频对讲:", streamUrl);
        // TODO: 实现WebRTC音频对讲功能
        this.isAudio = true;
        this.showStatus("WebRTC音频对讲已开启");
        return;
      }

      // 兼容旧的WebSocket方式调用 voice_pull_vms2(wss, wsChannelId)
      const wss = streamType;
      const wsChannelId = streamUrl;

      if (this.recorders) {
        this.recorders.openWebSocket(wss, wsChannelId);
        this.isAudio = true;
        this.showStatus("对讲已开启");
      } else {
        console.warn("音频录制器未初始化");
        this.showStatus("音频录制器未初始化");
      }
    },
    // 切换音频对讲
    toggleAudio() {
      if (this.isAudio) {
        this.stopAudio();
      } else {
        this.startAudio();
      }
    },
    // 停止所有流 - 参考video.html实现
    stopAll() {
      this.showStatus("正在停止...");

      // 停止音频
      if (this.isAudio) {
        this.stopAudio();
      }

      // 停止视频流
      if (this.currentHostbody && this.currentSn) {
        // 调用接口停止视频流
        const stopData = {
          hostbody_arr: [this.currentHostbody],
          sn_arr: [this.currentSn]
        };

        console.log("调用stopLive接口停止视频流:", stopData);

        this.stopLive(stopData).then(res => {
          if (res.data && res.data.code == 200) {
            console.log("成功停止视频流");
          } else {
            console.warn("停止视频流失败:", res.data);
          }
        }).catch(err => {
          console.error("停止视频流接口调用失败:", err);
        });

        // 重置sn值
        this.currentSn = null;
      }

      // 停止视频播放器
      if (this.player) {
        // 使用WebRTC播放器的正确关闭方法
        if (typeof this.player.close_play === 'function') {
          this.player.close_play(this.$refs.video).then(() => {
            console.log("视频流已关闭");
          }).catch(err => {
            console.error("关闭视频流时出错:", err);
          });
        } else if (typeof this.player.close === 'function') {
          this.player.close();
          console.log("视频流已关闭");
        } else if (typeof this.player.stop === 'function') {
          this.player.stop();
          console.log("视频流已关闭");
        } else {
          console.warn("播放器没有有效的关闭方法");
          // 尝试直接清理视频元素
          if (this.$refs.video) {
            this.$refs.video.pause();
            this.$refs.video.srcObject = null;
            this.$refs.video.load();
          }
        }
        this.player = null;
      }

      // 清理视频元素样式
      if (this.$refs.video) {
        this.$refs.video.classList.remove("active");
      }

      this.showStatus("已停止");

      // 延迟关闭弹窗
      setTimeout(() => {
        this.$emit('close');
      }, 1000);
    },
    // 关闭所有资源 - 参考zfjly.js实现
    closeAll() {
      // 停止音频对讲
      if (this.isAudio) {
        this.stopAudio();
      }

      // 停止视频播放器
      if (this.player) {
        if (typeof this.player.close_play === 'function') {
          this.player.close_play(this.$refs.video).catch(err => {
            console.error("关闭视频流时出错:", err);
          });
        } else if (typeof this.player.close === 'function') {
          this.player.close();
        } else if (typeof this.player.stop === 'function') {
          this.player.stop();
        }
        this.player = null;
      }

      // 清理音频录制器
      if (this.recorders) {
        this.recorders.closeWebsocket();
        this.recorders = null;
      }

      if (this.recordersPlatform) {
        this.recordersPlatform.closeWebsocket();
        this.recordersPlatform = null;
      }

      // 清理心跳定时器
      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }

      // 关闭WebSocket连接
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      // 清理视频元素
      if (this.$refs.video) {
        this.$refs.video.pause();
        this.$refs.video.srcObject = null;
        this.$refs.video.load();
        this.$refs.video.classList.remove("active");
      }

      // 重置所有状态
      this.wsConnected = false;
      this.webRtcUrl = null;
      this.currentSn = null;
      this.isAudio = false;
      this.dataInfo_set = [];
      this.form = {
        hostbodyArr: null,
        hostbody: null
      };
      this.dataInfo_hostbody = null;
      this.num = 0;
      this.currentHostbody = null;
      this.wsChannelId = null;
      this.isStartAudio = 0;
      this.isVolumeOn = false;
    },
    // 显示/隐藏加载状态
    showLoading(show) {
      this.$refs.loading.style.display = show ? "flex" : "none";
    },
    // 显示状态文本
    showStatus(text) {
      this.$refs.statusText.textContent = text;
      this.$refs.statusText.style.display = text ? "block" : "none";
      console.log("状态:", text);
    },

    // 声音控制方法 - 参考zfjly.js实现
    toggleVolume() {
      if (!this.$refs.video) {
        console.warn("视频元素不存在，无法控制声音");
        return false;
      }

      if (!this.isVolumeOn) {
        // 开启声音
        this.$refs.video.muted = false;
        this.isVolumeOn = true;
        this.showStatus("声音已开启");
        console.log("声音已开启");
      } else {
        // 关闭声音
        this.$refs.video.muted = true;
        this.isVolumeOn = false;
        this.showStatus("声音已关闭");
        console.log("声音已关闭");
      }

      return this.isVolumeOn;
    },

    // 获取声音状态
    getVolumeStatus() {
      return {
        isVolumeOn: this.isVolumeOn,
        message: this.isVolumeOn ? "声音已开启" : "声音已关闭"
      };
    },

    // 设置声音状态
    setVolumeStatus(status) {
      this.isVolumeOn = status;
      if (this.$refs.video) {
        this.$refs.video.muted = !status;
      }
    },
  },
  watch: {
    visible(val) {
      if (val) {
        // 确保组件每次显示时都重新初始化
        this.$nextTick(() => {
          this.init();
        });
      } else {
        this.closeAll();
      }
    }
  }
};
</script>

<style scoped>
.rwgz-tc {
  position: relative;
  width: 1500px;
  height: 1000px;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
  border-radius: 57px;
}

.rw-title {
  position: absolute;
  top: 50px;
  z-index: 888;
  border-radius: 57px 57px 0 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 1% 3%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
  cursor: pointer;
}

.btn:hover {
  background-color: #40a9ff !important;
}

.btn:disabled {
  background-color: #d9d9d9 !important;
  cursor: not-allowed;
}
</style>