#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TinyPNG 图片压缩脚本
使用 TinyPNG API 压缩图片文件
"""

import os
import sys
import requests
import argparse
import getpass
from pathlib import Path
import time

class TinyPNGCompressor:
    def __init__(self, api_key):
        self.api_key = api_key
        self.api_url = "https://api.tinify.com/shrink"
        self.session = requests.Session()
        self.session.auth = ('api', api_key)
        self.compressed_count = 0
        self.total_count = 0
        self.total_saved_bytes = 0

    def compress_image(self, input_path, output_path=None):
        """
        使用 TinyPNG API 压缩单个图片

        Args:
            input_path: 输入图片路径
            output_path: 输出图片路径，如果为None则覆盖原文件

        Returns:
            bool: 压缩是否成功
        """
        if output_path is None:
            output_path = input_path

        try:
            # 获取原始文件大小
            original_size = os.path.getsize(input_path)
            original_size_mb = original_size / (1024 * 1024)

            print(f"正在压缩: {input_path} ({original_size_mb:.2f}MB)")

            # 读取图片文件
            with open(input_path, 'rb') as source:
                # 上传到 TinyPNG
                response = self.session.post(self.api_url, data=source.read())

            if response.status_code == 201:
                # 获取压缩后的图片URL
                compressed_url = response.headers['Location']

                # 下载压缩后的图片
                compressed_response = requests.get(compressed_url)

                if compressed_response.status_code == 200:
                    # 保存压缩后的图片
                    with open(output_path, 'wb') as output:
                        output.write(compressed_response.content)

                    # 计算压缩效果
                    compressed_size = os.path.getsize(output_path)
                    compressed_size_mb = compressed_size / (1024 * 1024)
                    saved_bytes = original_size - compressed_size
                    compression_ratio = (saved_bytes / original_size) * 100

                    self.total_saved_bytes += saved_bytes

                    print(f"  ✓ 压缩成功!")
                    print(f"  原始大小: {original_size_mb:.2f}MB")
                    print(f"  压缩后: {compressed_size_mb:.2f}MB")
                    print(f"  节省: {saved_bytes / (1024 * 1024):.2f}MB ({compression_ratio:.1f}%)")

                    return True
                else:
                    print(f"  ✗ 下载压缩图片失败: HTTP {compressed_response.status_code}")
                    return False

            elif response.status_code == 400:
                print(f"  ✗ 图片格式不支持或文件损坏")
                return False
            elif response.status_code == 401:
                print(f"  ✗ API Key 无效")
                return False
            elif response.status_code == 429:
                print(f"  ✗ API 调用次数超限")
                return False
            else:
                print(f"  ✗ 压缩失败: HTTP {response.status_code}")
                print(f"  响应: {response.text}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"  ✗ 网络错误: {str(e)}")
            return False
        except Exception as e:
            print(f"  ✗ 压缩失败: {str(e)}")
            return False

    def compress_directory(self, directory, max_size_mb=None, delay=0.5):
        """
        压缩目录下所有支持的图片

        Args:
            directory: 目标目录
            max_size_mb: 最大文件大小限制(MB)，None表示不限制
            delay: 请求间隔时间(秒)，避免API限流
        """
        supported_formats = ('.png', '.jpg', '.jpeg', '.webp')

        print(f"开始扫描目录: {directory}")
        print(f"支持格式: {', '.join(supported_formats)}")
        if max_size_mb:
            print(f"大小限制: {max_size_mb}MB")
        print(f"请求间隔: {delay}秒")
        print("-" * 60)

        # 收集所有需要压缩的文件
        files_to_compress = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.lower().endswith(supported_formats):
                    file_path = os.path.join(root, file)
                    file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

                    # 检查文件大小限制
                    if max_size_mb is None or file_size_mb >= max_size_mb:
                        files_to_compress.append(file_path)
                    else:
                        print(f"跳过 {file_path} (大小: {file_size_mb:.2f}MB < {max_size_mb}MB)")

        self.total_count = len(files_to_compress)
        print(f"找到 {self.total_count} 个需要压缩的文件")
        print("-" * 60)

        # 压缩文件
        for i, file_path in enumerate(files_to_compress, 1):
            print(f"[{i}/{self.total_count}] ", end="")

            if self.compress_image(file_path):
                self.compressed_count += 1

            # 添加延迟避免API限流
            if i < self.total_count and delay > 0:
                time.sleep(delay)

        # 输出总结
        print("-" * 60)
        print(f"压缩完成!")
        print(f"总文件数: {self.total_count}")
        print(f"成功压缩: {self.compressed_count}")
        print(f"失败数量: {self.total_count - self.compressed_count}")
        print(f"总共节省: {self.total_saved_bytes / (1024 * 1024):.2f}MB")

def main():
    parser = argparse.ArgumentParser(description='使用 TinyPNG API 压缩图片')
    parser.add_argument('--dir', default='src/assets', help='目标目录 (默认: src/assets)')
    parser.add_argument('--max-size', type=float, help='最小文件大小MB，小于此大小的文件将被跳过')
    parser.add_argument('--delay', type=float, default=0.5, help='请求间隔时间(秒) (默认: 0.5)')
    parser.add_argument('--api-key', help='TinyPNG API Key (如果不提供将提示输入)')

    args = parser.parse_args()

    # 检查目录是否存在
    if not os.path.exists(args.dir):
        print(f"错误: 目录 '{args.dir}' 不存在")
        sys.exit(1)

    # 获取 API Key
    api_key = args.api_key
    if not api_key:
        api_key = getpass.getpass("请输入 TinyPNG API Key: ")

    if not api_key:
        print("错误: 必须提供 API Key")
        sys.exit(1)

    # 创建压缩器并开始压缩
    compressor = TinyPNGCompressor(api_key)
    compressor.compress_directory(args.dir, args.max_size, args.delay)

if __name__ == '__main__':
    main()