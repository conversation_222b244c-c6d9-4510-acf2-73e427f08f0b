<template>
  <div class="top">
    <div class="item1">
      <div class='container'>
        <p class="title">管辖面积(平方千米)</p>
        <div class="number">
          {{num1}}
        </div>
      </div>
    </div>
    <div class="item2">
      <div class='container'>
        <p class="title">实时人口(人)</p>
        <div class="number">
          {{num2}}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import countTo from 'vue-count-to'
export default {
  name: 'index',
  components: {
    countTo
  },
  data() {
    return {
      city: "",
      year: "",
      num1: "10,942",
      num2: "7,816,503",
      topname: [
        { name: "金华市", code: 330700 },
        { name: "婺城区", code: 330702 },
        { name: "金东区", code: 330703 },
        { name: "兰溪市", code: 330781 },
        { name: "东阳市", code: 330783 },
        { name: "义乌市", code: 330782 },
        { name: "永康市", code: 330784 },
        { name: "浦江县", code: 330726 },
        { name: "武义县", code: 330723 },
        { name: "磐安县", code: 330727 },
      ],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city) {
      indexApi("/csdn_yjyp11", { area_name: city }).then((res) => {
        this.num1 = this.setAct(res.data[0].area_num);
      });
      let code = this.topname.find((a) => a.name == city).code;
      if (code) {
        indexApi("/csrk_ssrsldrs", { addressCode: code }).then((res) => {
          if(res.data[0]&&res.data[0].population_count){
            this.num2 = this.setAct(Number(res.data[0].population_count));
          }
        });
      } else {
        this.num2 = "00000";
      }
    },
    //科学计数法
    setAct(val) {
      if (!val) return val;
      var logo = "";
      var num = val;
      num = typeof (num) === 'string' ? num : String(num)
      if (Number(val) < 0) {
        logo = "-";
        num = val.split('-')[1];
      }
      const result = num.split("");
      let position = result.indexOf(".");
      position = position !== -1 ? position : result.length;
      while (position > 3) {
        position -= 3;
        result.splice(position, 0, ",");
      }
      return logo + result.join("")
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.top {
  width: 1228px;
  height: 185px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  position: absolute;
  left: calc(50% - 614px);
  top: 200px;
  z-index: 666;
}

.line {
  width: 2px;
  height: 130px;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(211, 234, 255, 0.99) 48%,
    rgba(255, 255, 255, 0) 100%
  );
}

.item1 {
  width: 526px;
  height: 161px;
  text-align: center;
  margin-top: 10px;
  background: url("@/assets/common/top1.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.item2 {
  width: 526px;
  height: 161px;
  text-align: center;
  margin-top: 10px;
  background: url("@/assets/common/top2.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  margin-left: 180px;
}

.title {
  color: #e3edff;
  font-size: 32px;
}

.number {
  display: inline-block;
  font-size: 45px;
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.number .numbg {
  display: inline-block;
  width: 20px;
  height: 48px;
  font-weight: 700;
  line-height: 48px;
  text-align: center;
  margin: 0 4px;
  border-radius: 8px;
}
</style>