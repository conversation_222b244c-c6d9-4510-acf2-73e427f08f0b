* {
    padding: 0;
    margin: 0;
}

body {
    color: #fff;
    font: 32px syMeduim, syMeduim !important;
}

ul,
ul li {
    list-style: none;
}

.lf {
    float: left;
}

/*左浮动*/
.rt {
    float: right;
}

/*右浮动*/
.clear {
    clear: both;
}

/*清除浮动*/
.cursor {
    cursor: pointer;
}

/*鼠标hover*/
.fw-n {
    font-weight: normal
}

.fw-b {
    font-weight: bold;
}

/*字体加粗*/
.italic {
    font-style: italic;
}

/*倾斜字体*/
.text-center {
    text-align: center;
}

/*字体居中*/
.text-right {
    text-align: right;
}

/*字体居右*/
.text-left {
    text-align: left;
}

/*字体居左*/
.mt10 {
    margin-top: 10px;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/*弹性盒子居中space-start*/
.flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

/*弹性盒子居中space-around布局*/
.flex-around {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

/*弹性盒子居中space-between布局*/
.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/*弹性盒子居中center布局*/
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-evenly {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.flex-align-center {
    display: flex;
    align-items: center;
}

.disNone {
    display: none;
}

/*字体渐变色发光-blue*/
.text-linear-blue {
    background-image: linear-gradient(180deg, #fff, #fff, #80e0ff, #00c0ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-white {
    background-image: linear-gradient(180deg, #83b8ff, #83b8ff, #fff, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-yellow {
    background-image: linear-gradient(180deg, #fff, #fff, #ffe2b0, #ffb637);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-mid-yellow {
    background-image: linear-gradient(180deg, #ffb637, #ffb637, #fff, #ffb637, #ffb637);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-mid-blue {
    background-image: linear-gradient(180deg, #00c0ff, #00c0ff, #fff, #00c0ff, #00c0ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-red {
    background-image: linear-gradient(180deg, #fff, #fff, #ffcdcd, #ff4949);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-orange {
    background-image: linear-gradient(180deg, #fff, #fff, #fec297, #fd852e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-green {
    background-image: linear-gradient(180deg, #f0ffd7, #f4f1ff, #a9db52, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-cyan {
    background-image: linear-gradient(180deg, #fff, #fff, #34e7e7, #27e8e8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-linear-purple {
    background-image: linear-gradient(180deg, #fff, #fff, #cb98e2, #b871d8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/*滚动条样式*/
.scrollbar {
    overflow-x: hidden;
    overflow-y: auto;
}

.scrollbar::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
}

.scrollbar::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #00c0ff;
}

.scrollbar::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    width: 5px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: rgba(0, 192, 255, .5);
}

/*能滚动但无滚动条样式*/
.scrollbar-no {
    overflow: hidden;
    overflow-y: scroll;
}

.scrollbar-no::-webkit-scrollbar {
    width: 0;
    height: 0;
}

/*字体*/
@font-face {
    font-family: "sy";
    /*src: url("fonts/SourceHanSansCN-Regular.otf");*/
}

.font-sy {
    font-family: "sy";
}

@font-face {
    font-family: "syMeduim";
    /*src: url("fonts/SourceHanSansCN-Medium.otf");*/
}

.font-syMeduim {
    font-family: "syMeduim";
}

@font-face {
    font-family: "syBold";
    /*src: url("fonts/SourceHanSansCN-Bold.otf");*/
}

.font-syBold {
    font-family: "syBold";
}

@font-face {
    font-family: "DIN";
    /*src: url("fonts/DIN-Bold.otf");*/
}

.font-DIN {
    font-family: "DIN";
}

@font-face {
    font-family: "bebas";
    /*src: url("fonts/BebasNeue.otf");*/
}

.font-bebas {
    font-family: "bebas";
}

/*字号*/
.fs-24 {
    font-size: 24px;
}

.fs-28 {
    font-size: 28px;
}

.fs-30 {
    font-size: 30px;
}

.fs-32 {
    font-size: 32px;
}

.fs-34 {
    font-size: 34px;
}

.fs-36 {
    font-size: 36px;
}

.fs-38 {
    font-size: 38px;
}

.fs-40 {
    font-size: 40px;
}

.fs-44 {
    font-size: 44px;
}

.fs-48 {
    font-size: 48px;
}

.fs-50 {
    font-size: 50px;
}

.fs-54 {
    font-size: 54px;
}

.fs-60 {
    font-size: 60px;
}

.fs-64 {
    font-size: 64px;
}

.fs-68 {
    font-size: 68px;
}

.fs-70 {
    font-size: 70px;
}

.fs-80 {
    font-size: 80px;
}

/*字体颜色*/
.color-ffce00 {
    color: #ffce00;
}

.color-d6e7f9 {
    color: #d6e7f9;
}

.color-22e8e8 {
    color: #22e8e8;
}

.bgLine1074 {
    width: 1074px;
}

.bgLine900 {
    width: 900px;
}

.bgLine860 {
    width: 860px;
}

.bgLine780 {
    width: 780px;
}

.bgLine560 {
    width: 560px;
}

.bgLine387 {
    width: 387px;
}

.bgLine205 {
    width: 205px;
}

.deadline {
    font-size: 32px;
    font-family: "syMeduim";
}

/*内容*/
.panel-item {
    width: 100%;
    height: calc(100% - 110px);
}

/*标题上的下拉列表*/
.panel-drop {
    box-sizing: border-box;
    width: 251px;
    height: 55px;
    line-height: 55px;
    background: rgba(0, 28, 54, .7);
    border-radius: 40px;
    border: 1px solid #359cf8;
    margin-right: 30px;
    color: #fefefe;
    font-size: 32px;
    font-family: syMeduim;
    padding-left: 32px;
    cursor: pointer;
}

.drop-text {
    width: 70%;
    float: left;
}

.arrActive .drop-arr {
    transform: rotate(180deg);
}

.drop-con {
    box-sizing: border-box;
    width: 233px;
    /*height: 120px;*/
    background: rgba(0, 28, 54, .9);
    border: 1px solid #359cf8;
    position: absolute;
    left: 1217px;
    top: 1416px;
    z-index: 1;
    border-radius: 10px;
    color: #fefefe;
    font-size: 32px;
    font-family: syMeduim;
    /*padding-left: 23px;*/
    text-align: center;
    line-height: 55px;
    display: none;
    cursor: pointer;
}

/*tooltip--title样式*/
.tooltip {
    display: inline-block;
    width: 400px;
    height: auto;
    border: 1px solid #36a5ed;
    background: #000;
    color: #fff;
    position: absolute;
    padding: 5px;
    font-size: 42px;
    z-index: 300;
    word-break: break-all;
}

.anScale {
    animation: sclae 0.6s;
    animation-fill-mode: forwards;
}

@keyframes shake {
    10% {
        transform: rotate(15deg);
    }

    20% {
        transform: rotate(-10deg);
    }

    30% {
        transform: rotate(5deg);
    }

    40% {
        transform: rotate(-5deg);
    }

    50%,
    100% {
        transform: rotate(0deg);
    }
}

@keyframes sclae {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(1.1);
    }
}

@keyframes opacity {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

/*滚动数字样式*/
.box {
    height: 55px;
    overflow: hidden;
    margin-top: 0;
    margin-left: 30px;
    line-height: 0;
    display: flex;
}

.box-panel {
    margin-right: 10px;
}

.box span {
    display: inline-block;
    width: 41px;
    height: 55px;
    font-size: 50px;
    line-height: 55px;
    background-image: linear-gradient(180deg, #fff, #fff, #ffb637, #ffb637);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: "bebas";
}

.box .sign-box span {
    width: 41px;
    text-align: center;
}

.box .digit-container {
    width: 41px;
    text-align: center;
    overflow: hidden;
    font-size: 0;
}

/*数字滚动样式结束*/
/*layer*/
.layui-layer {
    min-width: 400px;
    text-align: center;
    border: 4px solid #28a2da;
    box-shadow: 0 0 50px inset #2fa9dd !important;
    background-color: rgba(2, 38, 78, .9) !important;
    border-radius: 20px !important;
}

.layui-layer-dialog {
    min-width: 400px !important;
}

.layui-layer-dialog .layui-layer-content {
    font-size: 40px !important;
    text-align: center;
    margin: 20px 0;
}

.layui-layer-page .layui-layer-content {
    font-size: 40px !important;
    margin: 20px 0;
}

.layui-layer-btn {
    text-align: center !important;
    padding: 23px 15px 27px !important;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.layui-layer-btn a {
    height: 38px !important;
    line-height: 38px !important;
}

.layui-layer-btn .layui-layer-btn0 {
    background-image: linear-gradient(0deg, #2b5e78 0%, #62e4fe 100%), linear-gradient(#3e4af6, #3e4af6);
    background-blend-mode: normal, normal;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.6);
    border: none !important;
}

.layui-layer-btn .layui-layer-btn1 {
    background-image: linear-gradient(0deg, #005bea 0%, #00c6fb 100%), linear-gradient(#3e4af6, #3e4af6);
    background-blend-mode: normal, normal;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.6);
    border: none !important;
    color: #fff !important;
}

.layui-layer-hui {
    border: 4px solid #28a2da !important;
}

.icon-phone {
    display: inline-block;
    background: url("../zhdd/icon_call.png") no-repeat;
    width: 32px;
    height: 32px;
    margin-left: 3%;
}

.input-wrap {
    width: 30%;
    height: 55px;
    line-height: 55px;
    outline: none;
    border: 1px solid #b1daf5;
    background: rgba(10, 97, 158, .2);
    border-radius: 10px;
    color: #fefefe;
    font-size: 28px;
    font-family: "sy";
    box-sizing: border-box;
    padding: 0 10px;
}

.zhdd-search {
    background: url("../zhdd/zhdd_search.png") no-repeat;
    width: 52px;
    height: 53px;
    cursor: pointer;
}

/*layui-label*/
.zhdd-item .layui-form-label {
    width: 22%;
    /*height: 36px;*/
    line-height: 36px;
    font-size: 34px;
}

/*.layui-form-label:before*/
.layui-form-item .layui-input-inline {
    width: 70%;
}

.zhdd-item .layui-input,
.zhdd-item .layui-select,
.zhdd-item .layui-textarea {
    /*border: 1px solid #cbe5f9;*/
    border: 1px solid #004477;
    background: rgba(10, 97, 158, .2);
    box-shadow: none;
    color: #fefefe;
}

/*layui-下拉框*/
.layui-form-select dl {
    background-color: rgba(2, 16, 30, 0.8) !important;
    color: #fefefe;
    border: 1px solid #065aa5;
}

.layui-form-select dl dd.layui-this {
    background-color: #1c4d65;
    /*color: #2fb4ff;*/
}

.layui-form-select .layui-edge {
    border-top-color: #fefefe;
}

.layui-form-select dl {
    top: 62px;
}

.layui-form-select dl dd:hover {
    background-color: #1c4d65 !important;
}

.layui-form-select dl dd,
.layui-form-select dl dt {
    line-height: 46px;
}

.layui-form .layui-input-block {
    margin-left: 232px;
    width: 85%;
}

.layui-textarea {
    resize: none;
    padding: 0 0 0 10px;
}

.layui-input::-webkit-input-placeholder {
    color: #fefefe;
    font-size: 32px;
    padding-left: 10px !important;
    margin: 0 !important;
    height: 60px;
    line-height: 60px;
}

.layui-anim-upbit::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
}

.layui-anim-upbit::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #00c0ff;
}

.layui-anim-upbit::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    width: 5px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: rgba(0, 192, 255, .5);
}

/*滑块*/
.layui-slider {
    height: 11px;
    background: #157597;
}

.layui-slider-wrap-btn {
    width: 22px;
    height: 22px;
}

.layui-slider-wrap {
    width: 56px;
    height: 56px;
    top: -22px;
    z-index: 0;
}

.layui-slider-tips {
    height: 36px;
    line-height: 36px;
}

/*layer*/
.layui-layer {
    min-width: 400px;
    text-align: center;
    border: 4px solid #28a2da;
    box-shadow: 0 0 50px inset #2fa9dd !important;
    background-color: rgba(2, 38, 78, .9) !important;
    border-radius: 20px !important;
}

.layui-layer-dialog {
    min-width: 400px !important;
}

.layui-layer-dialog .layui-layer-content {
    font-size: 40px !important;
    text-align: center;
    margin: 20px 0;
}

.layui-layer-page .layui-layer-content {
    font-size: 40px !important;
    margin: 20px 0;
}

.layui-layer-btn {
    text-align: center !important;
    padding: 23px 15px 27px !important;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.layui-layer-btn a {
    height: 38px !important;
    line-height: 38px !important;
}

.layui-layer-btn .layui-layer-btn0 {
    background-image: linear-gradient(0deg, #2b5e78 0%, #62e4fe 100%), linear-gradient(#3e4af6, #3e4af6);
    background-blend-mode: normal, normal;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.6);
    border: none !important;
}

.layui-layer-btn .layui-layer-btn1 {
    background-image: linear-gradient(0deg, #005bea 0%, #00c6fb 100%), linear-gradient(#3e4af6, #3e4af6);
    background-blend-mode: normal, normal;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.6);
    border: none !important;
    color: #fff !important;
}

.layui-layer-hui {
    border: 4px solid #28a2da !important;
}

.mid-icon {
    border: 1px solid #67b6f9;
    background: #1a2a47;
    width: 50px;
    height: 50px;
    margin-top: 10px;
    cursor: pointer;
    z-index: 998;
    box-sizing: border-box;
}

.mid-icon:hover {
    background: #1e66b5;
}
