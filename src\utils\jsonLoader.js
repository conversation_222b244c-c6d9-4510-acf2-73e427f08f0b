/**
 * JSON文件加载工具
 * 支持gzip压缩优化
 */

/**
 * 加载JSON文件的通用方法
 * @param {string} path - JSON文件路径
 * @returns {Promise} - 返回JSON数据的Promise
 */
export function loadJsonFile(path) {
  // 对于静态JSON文件，直接使用fetch更可靠
  return fetch(path, {
    headers: {
      'Accept-Encoding': 'gzip, deflate, br',
      'Content-Type': 'application/json',
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`加载JSON文件失败: ${path}, 状态码: ${response.status}`)
      }
      return response.json()
    })
    .catch((error) => {
      console.error('JSON文件加载错误:', error)
      throw error
    })
}

/**
 * 加载GeoJSON文件
 * @param {string} filename - 文件名（不含路径和扩展名）
 * @returns {Promise} - 返回GeoJSON数据的Promise
 */
export function loadGeoJson(filename) {
  // 使用相对路径，兼容Vue的publicPath配置
  const path = `./geoJson/${filename}.json`
  return loadJsonFile(path)
}

/**
 * 批量加载多个GeoJSON文件
 * @param {string[]} filenames - 文件名数组
 * @returns {Promise} - 返回所有GeoJSON数据的Promise
 */
export function loadMultipleGeoJson(filenames) {
  const promises = filenames.map((filename) => loadGeoJson(filename))
  return Promise.all(promises)
}
