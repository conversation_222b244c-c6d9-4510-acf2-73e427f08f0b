<template>
  <div>
    <CommonTitle :text='SetTitle()' @click.native="changePage()"></CommonTitle>
    <div class="box">
      <div class="yyItem" v-for="(el,i) in yyjcList" :key="i" @click="xjzxItemClick(el)" :class="{xsq:city != '金华市'}">
        <span class='appName'>{{el.name}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import xjzx from '@/assets/data/xjzx.json'
import CommonTitle from '@/components/CommonTitle'
import { getPalink, getUrl, getYklink } from '@/api/home'
import { mapActions } from 'vuex'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      yyjcList: []
    }
  },
  computed: {},
  created() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    ...mapActions('common', ['updateActiveCityName']),
    initApi(city,year) {
      if (city == "金华市") {
        this.yyjcList = xjzx.data;
      } else {
        indexApi("/xzzfj_yyji_list", { area: city }).then(res => {
          this.yyjcList = res.data.map(item => {return {
            name: item.name,
            url: item.url,
            city: item.area,
            img: item.picture_url
          }})
        })
      }
    },
    changePage() {
      let routePage = this.SetTitle() == '县级中心'?'/xjzx':'/yyjc'
      this.$router.push(routePage)
    },
    SetTitle() {
      return this.city == '金华市'?'县级中心':'应用集成'
    },
    xjzxItemClick(el) {
      if (el.key) {
        this.updateActiveCityName(el.city);
        this.$bus.$emit("cityChange", el.city);

      } else if (el.isDddl) {
        //是否单点登录
        if (el.name == "磐安县行政执法指挥中心") {
          getPalink(JSON.stringify({
            appId: "fc06313f1c824351ab855604366f27bc",
            appSecret: "d83f902a2e344c41bdd3e3a7a8eea9f3",
            timeStamp: Date.now(),
            sign: md5("appId=fc06313f1c824351ab855604366f27bc&appSecret=d83f902a2e344c41bdd3e3a7a8eea9f3&timeStamp=" + Date.now() + "&key=message").toUpperCase(),
          })).then(res => {
            console.log(res.data.token);
            window.open(window.location.origin + "/paxmd/sso/login?token=" + res.data.token + "&userName=pazhzx&loginType=CABIN")
          })
        }
      } else {
        this.openWebWin(el)
      }
    },
    openWebWin(item) {
      let that = this;
      let name = item.name;
      let url = item.url;
      if (url == "") {
        this.$message.error("暂无该大屏信息");
        return false;
      }
      if (url == "currentUrlProject") {
        getUrl(item.city).then(res => {
          top.window.location = res.data.data.url
        })
      } else if (name == "金华永康市永城数治系统") {
        // 金华永康市永城数治系统
        getYklink({appId: "330700_tsapp_056",userId: ''}).then(res => {
          if (res.code === 200) {
            that.openHtmlByMode(
              url + "&csdnCode=" + res.tempAuthCode,
              3840,
              2160
            );
          } else {
            this.$message.error("暂无权限");
          }
        })
      } else {
        that.openHtmlByMode(url, 3840, 2160);
      }
    },
    openHtmlByMode(url, width, higth) {
      let moveLeft = (7680 - width) / 2;
      let moveHigth = (2160 - higth) / 2;
      window.open(
        url,
        "项目接入系统",
        "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
        higth +
        ", width=" +
        width +
        ", top=" +
        moveHigth +
        ", left=" +
        moveLeft +
        ""
      );
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.box {
  width: 1200px;
  height: 900px;
  padding: 20px 40px;
  box-sizing: border-box;
  overflow-y: scroll;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  align-content: flex-start;
  .yyItem {
    width: 470px;
    height: 100px;
    background: url("@/assets/common/yyBg.png");
    background-size: 100% 100%;
    margin-bottom: 50px;
    cursor: pointer;
    margin-right: 20px;
    .appName {
      width: 100%;
      height: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 32px;
      line-height: 100px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      background: linear-gradient(90deg, #79FEFD 0%, #BEE1FF 100%);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
  .xsq {
    width: 940px;
    background-size: 100% 100%;
    //background: url("@/assets/common/yyjcBg2.png");
  }
}
</style>