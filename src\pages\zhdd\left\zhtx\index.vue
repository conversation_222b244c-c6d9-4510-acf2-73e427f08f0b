<template>
  <div style="margin-bottom: 40px">
    <CommonTitle text="指挥体系" @click.native='showDialog = true'>
      <div class="zhddTitleBar">
        <img
          v-if="manageUrl != ''"
          src="@/assets/common/edit.png"
          alt=""
          style="margin-left: 10px; cursor: pointer"
          @click="openManage()"
        />
      </div>
    </CommonTitle>
    <div class="tabChange">
      <div
        class="tabItem"
        v-for="(item, index) in tab"
        :class="{ tabActive: activeIndex === index }"
        @click="changeTab(item, index)"
      >
        {{ item }}
      </div>
    </div>
    <div class="zhtx">
      <div class="table">
        <div class="th">
          <div class="th_td" style="flex: 0.25; text-align: center">
            {{ activeItem == '乡镇街道指挥中心' ? '乡镇街道' : '部门' }}
          </div>
          <div class="th_td" style="flex: 0.15; text-align: left">职务</div>
          <div class="th_td" style="flex: 0.15; text-align: center">姓名</div>
          <div class="th_td" style="flex: 0.4; text-align: center">联系方式</div>
        </div>
        <div class="tbody" id="box2" @mouseenter="mouseenterEvent2" @mouseleave="mouseleaveEvent2">
          <div v-show="activeItem == '市直部门'">
            <div class="tr" v-for="(item, index) in szbmTable" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
          <div v-show="activeItem == '县市区指挥中心'">
            <div class="tr" v-for="(item, index) in xsqTableData" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
          <div v-show="activeItem == '乡镇街道指挥中心'">
            <div class="tr" v-for="(item, index) in xzjdTableData" :key="index">
              <div class="tr_td" style="flex: 0.25; text-align: center" :title="item.bm">
                {{ item.bm }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.zw }}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{ item.name }}
              </div>
              <div class="tr_td" style="flex: 0.4; text-align: center">
                <div style="display: flex; justify-content: space-around; align-items: center">
                  <div>{{ toPhone(item.phone) }}</div>
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 通话管理器组件 -->
    <CallManager
      ref="callManager"
      :config="callConfig"
      remote-view-id="remoteView_zhtx_5g"
      self-view-id="selfView_zhtx_5g"
      @initialized="onCallManagerInitialized"
      @error="onCallManagerError"
      @call-ended="onCallEnded"
      @signed-out="onSignedOut"
    />
    <zhtxDialog :visible='showDialog' @close='showDialog = false'></zhtxDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import zhtxDialog from '@/pages/zhdd/left/zhtx/zhtxDialog'
import CallManager from '@/components/CallManager'
import { getZhtxList } from '@/api/zhdd'
export default {
  name: 'index',
  components: {
    CommonTitle,
    zhtxDialog,
    CallManager
  },
  data() {
    return {
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      manageUrl: process.env.VUE_APP_BASE_ADMIN_URL+'/industryApp/xzzfzhzx/dutyPersonnel',
      activeItem: '市直部门',
      activeIndex: 0,
      tab: ['市直部门', '县市区指挥中心'],
      dom2: null,
      time2: null,
      szbmTable: [],
      xsqTableData: [],
      xzjdTableData: [],
      // 通话配置
      callConfig: {
        tenantType: 1,
        ip: 'ygf.xzzfj.jinhua.gov.cn',
        port: '443',
        vccId: '100317',
        agentId: '1001',
        password: 'Zyzx@10086',
        loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
      },
      showDialog: false
    }
  },
  beforeDestroy() {
    // CallManager组件会自动处理清理工作
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initApi(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.initApi(localStorage.getItem('city'), year)
    })
    this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
    // 表格滚动
    this.dom2 = document.getElementById('box2')
    this.mouseleaveEvent2()
  },
  methods: {
    // 通话管理器事件处理
    onCallManagerInitialized() {
      console.log('通话管理器初始化成功')
    },

    onCallManagerError(error) {
      console.error('通话管理器错误:', error)
      this.$message.error('通话系统初始化失败')
    },

    onCallEnded() {
      console.log('通话已结束')
    },

    onSignedOut() {
      console.log('已退出通话系统')
    },

    // 发起语音通话
    openCall(phone) {
      if (this.$refs.callManager) {
        this.$refs.callManager.makeVoiceCall(phone)
      }
    },

    // 发起视频通话
    openVideo(phone) {
      if (this.$refs.callManager) {
        this.$refs.callManager.makeVideoCall(phone)
      }
    },
    initApi(city) {
      this.initZhtx(city)
      if (city == '金华市') {
        this.tab = ['市直部门', '县市区指挥中心']
      } else {
        this.tab = ['县市区指挥中心', '乡镇街道指挥中心']
      }
      this.activeItem = this.tab[0]
    },
    //初始化指挥体系
    initZhtx(city) {
      this.getSzbm(city)
      this.getXsqzhzx(city)
      this.getXzjdzhzx(city)
    },
    //市直部门
    getSzbm(city) {
      getZhtxList({ area: city, type: '1' }).then((res) => {
        if (res.code == 200) {
          this.szbmTable = res.data.map((item) => {
            return {
              bm: item.deptName,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    //县市区指挥中心
    getXsqzhzx(city) {
      getZhtxList({ area: city, type: '2' }).then((res) => {
        if (res.code == 200) {
          this.xsqTableData = res.data.map((item) => {
            return {
              bm: item.deptName,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    //乡镇街道指挥中心
    getXzjdzhzx(city) {
      getZhtxList({ area: city, type: '3' }).then((res) => {
        if (res.code == 200) {
          this.xzjdTableData = res.data.map((item) => {
            return {
              bm: item.town,
              name: item.name,
              zw: item.duties,
              phone: item.phone,
            }
          })
        }
      })
    },
    openManage() {
      window.open(this.manageUrl)
    },
    changeTab(item, index) {
      this.activeIndex = index
      this.activeItem = item
    },
    toPhone(phone) {
      var reg = /(\d{3})\d{4}(\d{4})/ //正则表达式
      return phone.replace(reg, '$1****$2')
    },
    mouseenterEvent2() {
      clearInterval(this.time2)
    },
    mouseleaveEvent2() {
      this.time2 = setInterval(() => {
        // this.dom1.scrollTop += 1.5
        this.dom2.scrollBy({
          top: 93,
          behavior: 'smooth',
        })
        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
          this.dom2.scrollTop = 0
        }
      }, 1500)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.zhddTitleBar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.Phoneicon {
  width: 32px;
  height: 32px;
  background: url('@/assets/images/phone-icon.png') no-repeat;
  background-size: contain;
}
.Videoicon {
  width: 32px;
  height: 32px;
  background: url('@/assets/images/video-icon.png') no-repeat;
  background-size: contain;
}
.tabChange {
  display: flex;
  font-size: 30px;
  position: relative;
  top: 0;
  left: 0;
  color: #fff;
  justify-content: flex-start;
  align-items: center;
}
.tabItem {
  height: 59px;
  line-height: 59px;
  cursor: pointer;
  margin-left: 40px;
  padding-bottom: 5px;
  border-bottom: 4px solid transparent;
  box-sizing: border-box;
  white-space: nowrap;
}
.tabActive {
  width: 236px;
  height: 59px;
  color: #ffffff;
  background: url('@/assets/common/activeBg.png') no-repeat;
  background-size: 100% 100%;
  text-align: center;
  line-height: 59px;
}
.zhtx {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: 505px;
  overflow: hidden;
}
/* 表格 */
.table {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.table .th {
  width: 100%;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 28px;
  line-height: 60px;
  color: #dcefff;
  background: #09265a;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 80px);
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 92px;
  line-height: 88px;
  padding-top: 0px;
  font-size: 28px;
  color: #dcefff;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table .tr:nth-child(2n) {
  background: #091e45;
}

.table .tr:nth-child(2n + 1) {
  background: #0a1532;
}

.table .tr:hover {
  background-color: #0074da75;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table .tr_td > img {
  position: relative;
  top: 25px;
}

// 通话相关样式已移至独立的通话组件中
</style>