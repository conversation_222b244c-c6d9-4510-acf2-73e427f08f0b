<template>
  <div class="call-manager">
    <!-- 语音通话组件 -->
    <VoiceCall
      :visible.sync="voiceCallVisible"
      :phone-number="currentPhoneNumber"
      :is-connected="isCallConnected"
      :is-muted="isMuted"
      :duration="callDuration"
      @mute-toggle="handleMuteToggle"
      @call-end="handleCallEnd"
    />
    
    <!-- 视频通话组件 -->
    <VideoCall
      :visible.sync="videoCallVisible"
      :phone-number="currentPhoneNumber"
      :is-connected="isCallConnected"
      :duration="callDuration"
      :remote-view-id="remoteViewId"
      :self-view-id="selfViewId"
      @call-end="handleCallEnd"
    />
  </div>
</template>

<script>
import VoiceCall from '@/components/VoiceCall'
import VideoCall from '@/components/VideoCall'

export default {
  name: 'CallManager',
  components: {
    VoiceCall,
    VideoCall
  },
  props: {
    // VccBar配置
    config: {
      type: Object,
      default: () => ({
        tenantType: 1,
        ip: 'ygf.xzzfj.jinhua.gov.cn',
        port: '443',
        vccId: '100317',
        agentId: '1001',
        password: 'Zyzx@10086',
        loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF'
      })
    },
    // 远程视频容器ID
    remoteViewId: {
      type: String,
      default: 'remoteView_cincc_5g'
    },
    // 本地视频容器ID
    selfViewId: {
      type: String,
      default: 'selfView_cincc_5g'
    }
  },
  data() {
    return {
      // VccBar实例
      VccBar: null,
      // 是否已签入
      isSignedIn: false,
      // 是否已加载
      isLoaded: false,
      
      // 通话状态
      voiceCallVisible: false,
      videoCallVisible: false,
      currentPhoneNumber: '',
      isCallConnected: false,
      isMuted: false,
      callDuration: 0,
      callTimer: null
    }
  },
  mounted() {
    this.initializeVccBar()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 初始化VccBar
    async initializeVccBar() {
      try {
        if (this.isSignedIn) {
          this.VccBar.UnInitial()
        } else {
          // 设置配置和事件回调
          const vccConfig = {
            ...this.config,
            event: {
              OnInitalSuccess: this.onInitialSuccess,
              OnInitalFailure: this.onInitialFailure,
              OnAnswerCall: this.onAnswerCall,
              OnCallEnd: this.onCallEnd,
              OnReportBtnStatus: this.onReportBtnStatus,
              OnCallRing: this.onCallRing,
              OnBarExit: this.onBarExit,
              OnUpdateVideoWindow: this.onUpdateVideoWindow,
              OnAgentWorkReport: this.onAgentWorkReport
            }
          }
          
          // 如果已经加载过cmscVccBar，不再重新加载
          if (!this.isLoaded) {
            this.VccBar = VccBar.setConfig(vccConfig).client()
            await this.VccBar.load('cmscVccBar')
            this.isLoaded = true
            this.VccBar.Initial()
          } else {
            this.VccBar.Initial()
          }
        }
      } catch (error) {
        console.error('VccBar初始化失败:', error)
        this.$emit('error', error)
      }
    },
    
    // 发起语音通话
    makeVoiceCall(phoneNumber) {
      if (!phoneNumber) {
        this.$message.info('此人暂未录入手机号码')
        return
      }
      
      this.currentPhoneNumber = phoneNumber
      this.voiceCallVisible = true
      this.isMuted = false
      this.isCallConnected = false
      this.callDuration = 0
      
      if (this.VccBar) {
        this.VccBar.MakeCall(phoneNumber, 3, '', '', '', '', '', '', '', '', 1)
      }
    },
    
    // 发起视频通话
    makeVideoCall(phoneNumber) {
      if (!phoneNumber) {
        this.$message.info('此人暂未录入手机号码')
        return
      }
      
      this.currentPhoneNumber = phoneNumber
      this.videoCallVisible = true
      this.isCallConnected = false
      this.callDuration = 0
      
      if (this.VccBar) {
        this.VccBar.MakeCall(phoneNumber, 3, '', '', '', '', '', '', '', '', 2)
      }
    },
    
    // 处理静音切换
    handleMuteToggle(muted) {
      this.isMuted = muted
      if (this.VccBar) {
        this.VccBar.Mute(muted ? 2 : 1)
      }
    },
    
    // 处理通话结束
    handleCallEnd() {
      if (this.VccBar) {
        this.VccBar.Disconnect()
      }
      this.endCall()
    },
    
    // 结束通话
    endCall() {
      this.voiceCallVisible = false
      this.videoCallVisible = false
      this.isCallConnected = false
      this.callDuration = 0
      this.clearCallTimer()
    },
    
    // 开始计时
    startCallTimer() {
      this.callTimer = setInterval(() => {
        this.callDuration++
      }, 1000)
    },
    
    // 清除计时器
    clearCallTimer() {
      if (this.callTimer) {
        clearInterval(this.callTimer)
        this.callTimer = null
      }
    },
    
    // 清理资源
    cleanup() {
      this.clearCallTimer()
      if (this.VccBar && this.isSignedIn) {
        this.VccBar.UnInitial()
      }
    },
    
    // VccBar事件回调
    onInitialSuccess() {
      this.isSignedIn = true
      console.log('VccBar初始化成功')
      this.$emit('initialized')
    },
    
    onInitialFailure() {
      console.log('VccBar初始化失败')
      this.$emit('error', new Error('VccBar初始化失败'))
    },
    
    onAnswerCall(userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent) {
      this.isCallConnected = true
      this.startCallTimer()
      console.log('通话接通')
      
      if (av === 'video') {
        // 视频通话相关处理
        this.$emit('video-connected')
      }
    },
    
    onCallEnd(callID, serialID, serviceDirect, userNo, bgnTime, endTime, agentAlertTime, userAlertTime, fileName, directory, disconnectType, userParam, taskID, serverName, networkInfo) {
      console.log('通话结束')
      this.endCall()
      this.$emit('call-ended')
    },
    
    onReportBtnStatus(btnIDS) {
      console.log('按钮状态报告:', btnIDS)
    },
    
    onCallRing(callingNo, calledNo, orgCalledNo, callData, serialID, serviceDirect, callID, userParam, taskID, userDn, agentDn, areaCode, fileName, networkInfo, queueTime, opAgentID, ringTime, projectID, accessCode, taskName, cityName, userType, lastServiceId, lastServiceName, accessNumber) {
      console.log('电话振铃')
    },
    
    onBarExit(code, description) {
      this.isSignedIn = false
      console.log('坐席迁出')
      this.$emit('signed-out')
    },
    
    onUpdateVideoWindow(param) {
      console.log('更新视频窗口:', param)
      if (param.key_word === 'GetVideoViews') {
        param.param.SetVideoViews(this.selfViewId, this.remoteViewId)
      }
    },
    
    onAgentWorkReport(workStatus, description) {
      console.log('坐席工作状态报告:', workStatus, description)
    }
  }
}
</script>

<style scoped lang="less">
.call-manager {
  // 这个组件主要是逻辑组件，不需要特殊样式
}
</style>
