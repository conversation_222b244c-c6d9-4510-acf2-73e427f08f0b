<template>
  <el-dialog
    v-if="visible"
    title="视频通话"
    :visible.sync="dialogVisible"
    :modal-append-to-body="false"
    :append-to-body="false"
    :show-close="false"
    width="836px"
    top="45vh"
    custom-class="video_call_dialog"
    @close="handleClose"
  >
    <div class="video_call_box">
      <div class="video_call_top">
        <div class="remoteView_box">
          <div class="remoteView" :id="remoteViewId"></div>
        </div>
        <div class="info_box">
          <div class="phone_number">{{ phoneNumber }}</div>
          <div class="status_box">
            <div class="status" :class="{ green_status: isConnected }">
              {{ isConnected ? '视频中' : '拨号中' }}
            </div>
            <img 
              v-if="isConnected" 
              class="status_img1" 
              src="@/assets/ajhf/phone_audio.png" 
              alt="" 
            />
            <img 
              v-else 
              class="status_img2" 
              src="@/assets/ajhf/phone_calling.png" 
              alt="" 
            />
          </div>
          <div class="voice_box">
            <img class="voice_img" src="@/assets/ajhf/phone_video.png" alt="" />
            <div class="voice_time">{{ formattedTime }}</div>
          </div>
          <div class="selfView_box">
            <div class="selfView" :id="selfViewId"></div>
          </div>
        </div>
      </div>
      <div class="video_call_center">
        <img 
          class="video_call_end" 
          @click="endCall" 
          src="@/assets/ajhf/phone_end.png" 
          alt="" 
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'VideoCall',
  props: {
    // 控制组件显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 电话号码
    phoneNumber: {
      type: String,
      default: ''
    },
    // 通话状态
    isConnected: {
      type: Boolean,
      default: false
    },
    // 通话时长（秒）
    duration: {
      type: Number,
      default: 0
    },
    // 远程视频容器ID
    remoteViewId: {
      type: String,
      default: 'remoteView_cincc_5g'
    },
    // 本地视频容器ID
    selfViewId: {
      type: String,
      default: 'selfView_cincc_5g'
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  computed: {
    // 格式化通话时长
    formattedTime() {
      const hours = Math.floor(this.duration / 3600)
      const minutes = Math.floor((this.duration % 3600) / 60)
      const seconds = this.duration % 60
      
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal
      },
      immediate: true
    },
    dialogVisible(newVal) {
      if (!newVal && this.visible) {
        this.$emit('update:visible', false)
      }
    }
  },
  methods: {
    // 结束通话
    endCall() {
      this.$emit('call-end')
    },
    
    // 处理对话框关闭
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="less">
/deep/.video_call_dialog {
  background: transparent;
  .el-dialog__header {
    background: rgba(61, 115, 255, 0.6);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid rgba(61, 115, 255, 0.72);
    border-bottom: none;
  }
  .el-dialog__title {
    font-weight: 500;
    font-size: 36px;
    color: #ffffff;
    line-height: 38px;
  }
  .el-dialog__body {
    background: rgba(16, 28, 63, 0.9);
    box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid rgba(61, 115, 255, 0.72);
  }
}

.video_call_box {
  text-align: center;
  
  .video_call_top {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    margin-top: 66px;
    
    .remoteView_box {
      width: 297px;
      height: 516px;
      background: url('@/assets/ajhf/video_left.png') 0 0 no-repeat;
      background-size: cover;
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: center;
      margin-right: 88px;
      
      .remoteView {
        width: 280px;
        height: 500px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
      }
    }
    
    .info_box {
      .phone_number {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 700;
        font-size: 40px;
        color: #fff;
        line-height: 48px;
        margin-bottom: 26px;
      }
      
      .status_box {
        display: flex;
        align-content: center;
        align-items: center;
        
        .status {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #2cd7aa;
          line-height: 48px;
          margin-right: 20px;
        }
        
        .green_status {
          color: #b2d8ff;
        }
        
        .status_img1 {
          width: 99px;
          height: 38px;
        }
        
        .status_img2 {
          width: 32px;
          height: 32px;
        }
      }
      
      .voice_box {
        display: flex;
        align-content: center;
        align-items: center;
        margin-top: 26px;
        
        .voice_img {
          width: 48px;
          height: 48px;
          margin-right: 32px;
        }
        
        .voice_time {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #fff;
          line-height: 48px;
        }
      }
      
      .selfView_box {
        width: 226px;
        height: 146px;
        background: url('@/assets/ajhf/video_right.png') 0 0 no-repeat;
        background-size: cover;
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: center;
        margin-top: 88px;
        
        .selfView {
          width: 210px;
          height: 130px;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 4px;
        }
      }
    }
  }
  
  .video_call_center {
    margin-top: 64px;
    
    .video_call_end {
      width: 96px;
      height: 96px;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
      }
    }
  }
}
</style>
