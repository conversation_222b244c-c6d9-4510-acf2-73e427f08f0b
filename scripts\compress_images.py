#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩脚本
压缩src/assets目录下超过1MB的图片文件
"""

import os
import sys
from PIL import Image
import argparse

def compress_image(input_path, output_path=None, quality=85, max_size_mb=1):
    """
    压缩单个图片文件
    
    Args:
        input_path: 输入图片路径
        output_path: 输出图片路径，如果为None则覆盖原文件
        quality: 压缩质量 (1-100)
        max_size_mb: 最大文件大小限制(MB)
    """
    if output_path is None:
        output_path = input_path
    
    try:
        # 获取文件大小
        file_size_mb = os.path.getsize(input_path) / (1024 * 1024)
        
        if file_size_mb <= max_size_mb:
            print(f"跳过 {input_path} (大小: {file_size_mb:.2f}MB)")
            return False
        
        # 打开图片
        with Image.open(input_path) as img:
            # 获取原始文件格式
            original_format = img.format
            _, ext = os.path.splitext(input_path)
            ext = ext.lower()

            # 计算新的尺寸（如果图片太大）
            max_dimension = 1920
            if max(img.size) > max_dimension:
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            # 根据原始格式保存
            if original_format == 'PNG' or ext == '.png':
                # PNG格式：保持透明度
                if img.mode == 'P':
                    img = img.convert('RGBA')
                img.save(output_path, 'PNG', optimize=True)
            elif original_format in ['JPEG', 'JPG'] or ext in ['.jpg', '.jpeg']:
                # JPEG格式：转换为RGB（JPEG不支持透明度）
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(output_path, 'JPEG', quality=quality, optimize=True)
            elif original_format == 'WEBP' or ext == '.webp':
                # WebP格式：保持原有模式
                img.save(output_path, 'WEBP', quality=quality, optimize=True)
            else:
                # 其他格式：尝试保持原格式，如果失败则转为PNG
                try:
                    if original_format:
                        img.save(output_path, original_format, quality=quality if original_format in ['JPEG', 'WEBP'] else None, optimize=True)
                    else:
                        # 如果无法确定原格式，根据扩展名决定
                        if ext in ['.bmp']:
                            img.save(output_path, 'BMP')
                        elif ext in ['.tiff', '.tif']:
                            img.save(output_path, 'TIFF')
                        else:
                            # 默认保存为PNG
                            if img.mode == 'P':
                                img = img.convert('RGBA')
                            img.save(output_path, 'PNG', optimize=True)
                except:
                    # 如果保存失败，转为PNG格式
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    img.save(output_path, 'PNG', optimize=True)
            
            # 检查压缩后的文件大小
            new_size_mb = os.path.getsize(output_path) / (1024 * 1024)
            compression_ratio = (1 - new_size_mb / file_size_mb) * 100
            
            print(f"压缩完成: {input_path}")
            print(f"  原始大小: {file_size_mb:.2f}MB")
            print(f"  压缩后: {new_size_mb:.2f}MB")
            print(f"  压缩率: {compression_ratio:.1f}%")
            
            return True
            
    except Exception as e:
        print(f"压缩失败 {input_path}: {str(e)}")
        return False

def compress_directory(directory, quality=85, max_size_mb=1):
    """
    压缩目录下所有图片
    
    Args:
        directory: 目标目录
        quality: 压缩质量
        max_size_mb: 最大文件大小限制(MB)
    """
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')
    compressed_count = 0
    total_count = 0
    
    print(f"开始扫描目录: {directory}")
    print(f"压缩设置: 质量={quality}, 大小限制={max_size_mb}MB")
    print("-" * 50)
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(supported_formats):
                total_count += 1
                file_path = os.path.join(root, file)
                
                if compress_image(file_path, quality=quality, max_size_mb=max_size_mb):
                    compressed_count += 1
    
    print("-" * 50)
    print(f"压缩完成! 共处理 {total_count} 个图片文件")
    print(f"成功压缩 {compressed_count} 个文件")

def main():
    parser = argparse.ArgumentParser(description='压缩图片文件')
    parser.add_argument('--dir', default='src/assets', help='目标目录 (默认: src/assets)')
    parser.add_argument('--quality', type=int, default=85, help='压缩质量 1-100 (默认: 85)')
    parser.add_argument('--max-size', type=float, default=1.0, help='最大文件大小MB (默认: 1.0)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.dir):
        print(f"错误: 目录 '{args.dir}' 不存在")
        sys.exit(1)
    
    compress_directory(args.dir, args.quality, args.max_size)

if __name__ == '__main__':
    main()