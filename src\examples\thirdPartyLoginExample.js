/**
 * 第三方登录使用示例
 * 这个文件展示了如何在项目中使用第三方登录接口
 */

import { loginByThird, loginByThirdAuto, loginByThirdComplete, generateTimestamp, generateVtxSign } from '@/api/thirdParty/login'
import { setToken } from '@/utils/auth'

/**
 * 示例1：基础第三方登录
 * 手动指定所有参数的登录方式
 */
export async function basicThirdPartyLogin() {
  try {
    const loginParams = {
      tenantCode: 'your_tenant_code',    // 替换为实际的租户代码
      account: 'your_account',           // 替换为实际的账户
      scope: 'your_scope'                // 替换为实际的作用域
    };

    const appKey = 'your_app_key';         // 替换为实际的应用密钥
    const appSecret = 'your_app_secret';   // 替换为实际的应用秘钥
    const timestamp = generateTimestamp(); // 生成当前时间戳
    const vtxSign = generateVtxSign(loginParams, appKey, appSecret, timestamp); // 自动生成签名

    const signHeaders = {
      vtxSignAppKey: appKey,
      vtxSignTimestamp: timestamp,
      vtxSign: vtxSign
    };

    const response = await loginByThird(loginParams, signHeaders);
    
    if (response && response.token) {
      // 登录成功，保存token
      setToken(response.token);
      console.log('第三方登录成功:', response);
      return response;
    } else {
      throw new Error('登录响应中没有token');
    }
  } catch (error) {
    console.error('第三方登录失败:', error);
    throw error;
  }
}

/**
 * 示例2：便捷第三方登录
 * 使用自动生成时间戳的便捷方法
 */
export async function autoThirdPartyLogin() {
  try {
    const loginParams = {
      tenantCode: 'your_tenant_code',
      account: 'your_account',
      scope: 'your_scope'
    };

    const signConfig = {
      appKey: 'your_app_key',
      appSecret: 'your_app_secret'  // 现在会自动生成签名
    };

    const response = await loginByThirdAuto(loginParams, signConfig);
    
    if (response && response.token) {
      setToken(response.token);
      console.log('第三方登录成功:', response);
      return response;
    } else {
      throw new Error('登录响应中没有token');
    }
  } catch (error) {
    console.error('第三方登录失败:', error);
    throw error;
  }
}

/**
 * 示例3：在Vue组件中使用第三方登录
 * 这是一个可以在Vue组件中使用的方法
 */
export const thirdPartyLoginMixin = {
  methods: {
    async handleThirdPartyLogin(loginData) {
      try {
        // 显示加载状态
        this.$loading = this.$loading({
          lock: true,
          text: '正在进行第三方登录...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        const { tenantCode, account, scope, appKey, appSecret } = loginData;

        const response = await loginByThirdAuto(
          { tenantCode, account, scope },
          { appKey, appSecret }
        );

        if (response && response.token) {
          setToken(response.token);
          this.$message.success('第三方登录成功！');
          
          // 跳转到主页或指定页面
          this.$router.push('/home');
          
          return response;
        } else {
          throw new Error('登录失败，请检查参数');
        }
      } catch (error) {
        console.error('第三方登录错误:', error);
        this.$message.error(`登录失败: ${error.message || '未知错误'}`);
        throw error;
      } finally {
        // 隐藏加载状态
        if (this.$loading) {
          this.$loading.close();
        }
      }
    }
  }
};

/**
 * 示例4：使用最简单的完整登录方法
 */
export async function simpleThirdPartyLogin() {
  try {
    const response = await loginByThirdComplete({
      tenantCode: 'your_tenant_code',
      account: 'your_account',
      scope: 'your_scope',
      appKey: 'your_app_key',
      appSecret: 'your_app_secret'
    });

    if (response && response.token) {
      setToken(response.token);
      console.log('第三方登录成功:', response);
      return response;
    } else {
      throw new Error('登录响应中没有token');
    }
  } catch (error) {
    console.error('第三方登录失败:', error);
    throw error;
  }
}

/**
 * 示例5：完整的登录流程（已经内置在loginByThirdComplete中）
 * 包含参数验证、签名生成、登录请求等完整流程
 */
export async function completeThirdPartyLogin(config) {
  const { tenantCode, account, scope, appKey, appSecret } = config;

  // 参数验证
  if (!tenantCode || !account || !scope || !appKey || !appSecret) {
    throw new Error('缺少必要的登录参数');
  }

  try {
    // 直接使用完整登录方法，内部会自动处理时间戳和签名生成
    const response = await loginByThirdComplete(config);

    return response;
  } catch (error) {
    console.error('完整登录流程失败:', error);
    throw error;
  }
}

/**
 * 使用说明：
 * 
 * 1. 在Vue组件中使用：
 *    import { thirdPartyLoginMixin } from '@/examples/thirdPartyLoginExample'
 *    export default {
 *      mixins: [thirdPartyLoginMixin],
 *      methods: {
 *        async login() {
 *          await this.handleThirdPartyLogin({
 *            tenantCode: 'xxx',
 *            account: 'xxx',
 *            scope: 'xxx',
 *            appKey: 'xxx',
 *            sign: 'xxx'
 *          });
 *        }
 *      }
 *    }
 * 
 * 2. 在普通JS中使用：
 *    import { basicThirdPartyLogin } from '@/examples/thirdPartyLoginExample'
 *    basicThirdPartyLogin().then(response => {
 *      console.log('登录成功', response);
 *    });
 * 
 * 3. 自定义签名算法：
 *    修改 generateSign 函数，实现符合第三方接口要求的签名算法
 */
