<template>
  <header class="header_img title-bar-container">
    <div
      class="city_select"
      v-show="routeName == '首页' || routeName == '指挥调度' || routeName == '执法态势' || routeName == '案件回访'"
    >
      <el-select v-model="active_cityName" placeholder="县市区" @change="cityFun" :popper-append-to-body="false">
        <el-option v-for="(item, i) in cityList" :label="item.name" :value="item.name" :key="i"></el-option>
      </el-select>
    </div>
    <div id="header_weather"></div>
    <div class="header_title_text" style="cursor: pointer">
      <span class="header_text" @click="backHome()">{{ getHeaderTitleText() }}</span>
    </div>
    <div class="year_select">
      <el-select
        v-model="currentYear"
        @change="yearFun"
        v-show="routeName == '首页' || routeName == '指挥调度' || routeName == '执法态势' || routeName == '案件回访'||routeName=='运行监测一张图'"
        :popper-append-to-body="false"
      >
        <el-option v-for="(item, i) in yearList" :label="item.name" :value="item.value" :key="i"></el-option>
      </el-select>
    </div>
    <div id="header_week"></div>
  </header>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import { mapState, mapActions, mapGetters } from 'vuex'
import moment from 'moment'
export default {
  name: 'index',
  data() {
    return {
      currentYear: moment().format('YYYY'), //当前下拉选中的年份（默认当前年份）
      cityList: [], //县市区下拉列表
      yearList: [
        {
          name: '2025年度',
          value: '2025',
        },
        {
          name: '2024年度',
          value: '2024',
        },
        {
          name: '2023年度',
          value: '2023',
        },
      ], //年份下拉列表
      localCityName: '', // 本地存储的城市名称，用于绑定select
    }
  },
  computed: {
    ...mapGetters('common', ['activeCityName']),
    active_cityName: {
      get() {
        return this.activeCityName
      },
      set(value) {
        // 这里不做任何操作，让cityFun处理
      },
    },
    adminCity() {
      return localStorage.getItem('adminCity') //登陆账户所属县市区
    },
    routeName() {
      return this.$route.name
    },
    routePath() {
      return this.$route.path
    },
  },
  mounted() {
    localStorage.setItem('year', this.currentYear)
    this.initActiveCityName() // 初始化城市名称
    this.getWeather()
    this.getCityList()

    // 设置定时器获取时间
    setInterval(() => {
      this.getNewTime()
    }, 1000)

    // 如果没有选中的城市，则使用管理员城市
    if (!this.activeCityName && this.adminCity) {
      this.cityFun(this.adminCity)
    }

    // 初始化设置页面标题
    this.updatePageTitle()
  },
  methods: {
    ...mapActions('common', ['updateActiveCityName', 'initActiveCityName']),
    getHeaderTitleText() {
      if (this.routeName == '菜单页' || this.routeName == '首页') {
        return this.adminCity + '行政执法指挥中心'
      } else {
        return this.routeName
      }
    },
    // 更新页面标题
    updatePageTitle() {
      const title = this.getHeaderTitleText()
      document.title = title
    },
    /**头部县市区选择 */
    cityFun(item) {
      if (!item || item === this.activeCityName) return // 避免重复设置相同的值

      this.updateActiveCityName(item)
      this.$bus.$emit('cityChange', item)
    },
    /**头部年份选择 */
    yearFun(item) {
      localStorage.setItem('year', item)
      this.$bus.$emit('yearChange', item)
    },
    backHome() {
      if (this.$route.path == '/dataView' || this.$route.path == '/monitor') {
        this.$router.push('/zqyzt')
      } else {
        this.$router.push('/home2')
      }
    },
    getCityList() {
      const cityList = [
        { name: '金华市', code: 330700 },
        { name: '婺城区', code: 330702 },
        { name: '金东区', code: 330703 },
        { name: '兰溪市', code: 330781 },
        { name: '东阳市', code: 330783 },
        { name: '义乌市', code: 330782 },
        { name: '永康市', code: 330784 },
        { name: '浦江县', code: 330726 },
        { name: '武义县', code: 330723 },
        { name: '磐安县', code: 330727 },
        { name: '金华开发区', code: 330751 },
      ]

      if (this.adminCity === '金华市') {
        this.cityList = cityList
      } else if (this.adminCity === '开发区') {
        this.cityList = [{ name: '金华开发区', code: 330751 }]
      } else {
        const foundCity = cityList.find((item) => item.name === this.adminCity)
        this.cityList = foundCity ? [foundCity] : []
      }
    },
    getNewTime() {
      let date = new Date()
      $('#nowTime').html(`
      ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`)
    },
    getWeather() {
      indexApi('/cstz_tqjk').then((res) => {
        let weaker = res.data
        let date = new Date()
        for (var i = 0; i < weaker.length; i++) {
          if (date.getDate().toString().padStart(2, '0') === weaker[i].days.padStart(2, '0')) {
            let imgUrl = require('@/assets/weather/' + weaker[i].weather_path.slice(17))
            let wd = weaker[i].low.split(' ')[1] + '~' + weaker[i].high.split(' ')[1]
            $('#header_weather').html(`
              <img style="width:45px;margin-right: 30px;" src="${imgUrl}"/> <span>${wd}</span>`)
            $('#header_week').html(`
              <span class="nowDate c-blue"><i class="iconfont icon-rili"></i>${weaker[i].ymd}</span>&nbsp;&nbsp;
              <span>${weaker[i].WEEK}</span>&nbsp;
              <span id="nowTime" class="c-blue">${date.getHours().toString().padStart(2, '0')}:
              ${date.getMinutes().toString().padStart(2, '0')}</span>`)
          }
        }
      })
    },
  },
  watch: {
    // 移除对active_cityName的监听，避免循环调用
    //监听路由页面变化
    $route(to, from) {
      if (to.name === '县级中心' || to.name === '应用集成') {
        const city = localStorage.getItem('city')
        if (city === '开发区') {
          this.cityList = [{ name: '金华开发区', code: 330751 }]
        } else {
          this.cityList = this.cityList.filter((item) => item.name === city)
        }
      } else {
        this.getCityList()
      }
      // 路由变化时更新页面标题
      this.$nextTick(() => {
        this.updatePageTitle()
      })
    },
    // 监听adminCity变化
    adminCity() {
      this.$nextTick(() => {
        this.updatePageTitle()
      })
    },
  },
}
</script>

<style scoped lang='less'>
/* 使用新的嵌套语法来处理深度选择器 */
.title-bar-container {
  /* 保持原有的头部样式 */
  background: url('@/assets/TitleBar/header_bg.png') no-repeat;
  width: 100% !important;
  height: 240px;
  z-index: 777;
  margin: 0 auto;
  top: 0;
  font-size: 38px;
  color: #fff;
  background-size: cover;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  position: fixed;

  /* 使用 :deep() 替换 /deep/ */
  :deep(.el-input) {
    width: 83% !important;
    margin-left: 12px;

    .el-input__inner {
      color: #fff !important;
      height: 100px !important;
      line-height: 95px !important;
      border-color: transparent !important;
      font-size: 48px !important;
      font-style: oblique;
      font-weight: bold;
      background: linear-gradient(
        0deg,
        #9eb7ef 1.8798828125%,
        #a3d0ff 50.244140625%,
        #ffffff 53.0029296875%,
        #ebf2ff 100%
      ) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }

    .el-input__suffix {
      right: -20px !important;
    }

    .el-input__icon {
      width: max-content !important;
    }
  }

  :deep(.el-select) {
    .el-input .el-select__caret {
      color: #eef0f5 !important;
      transform: rotateZ(0deg) !important;
      font-size: 35px !important;

      &.is-reverse {
        transform: rotateZ(180deg) !important;
      }
    }
  }

  :deep(.el-icon-arrow-up:before) {
    content: '\e790' !important;
  }

  :deep(.el-select-dropdown) {
    min-width: initial !important;
    background-color: #02103c94 !important;
    border: none !important;
    width: 280px !important;
    position: relative !important;
    top: 0px !important;
    left: 0px !important;
    .el-select-dropdown__item {
      text-align: center;
      font-size: 30px !important;
      color: #cfcfd6 !important;
      height: 48px !important;
      line-height: 48px !important;

      &.hover {
        background-color: #27508f !important;
      }
    }

    .el-select-dropdown__wrap {
      max-height: initial !important;
    }
  }

  :deep(.el-popper[x-placement^='bottom']) {
    margin-top: 0 !important;
  }

  :deep(.el-popper) {
    .popper__arrow,
    .popper__arrow::after {
      display: none !important;
    }
  }

  :deep(.el-scrollbar__wrap) {
    overflow-y: scroll !important;
    overflow-x: hidden !important;
    margin-right: 0 !important;
  }
}

/* 保持其他原有样式 */
.city_select {
  position: absolute;
  left: 0;
  width: 345px;
  height: 100px;
  background: url('@/assets/index/city_bg.png') no-repeat;
  background-size: 100%;
  z-index: 99;
}

.year_select {
  position: absolute;
  right: 700px;
  top: 20px;
  width: 300px;
  height: 80px;
  z-index: 99;
}

.header_title_text {
  position: absolute;
  width: 3838px;
  height: 219px;
  line-height: 140px;
  text-align: center;
  font-size: 95px;
  font-weight: bold;
  font-family: YouSheBiaoTiHei;
  left: calc(50% - 1920px);
  z-index: 2;
  text-align: center;
  background: url('@/assets/TitleBar/header-title-bg.png') no-repeat;
  background-size: cover;
}

.header_text {
  background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ebf2ff), to(#94aadb));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

#header_weather {
  width: max-content;
  position: absolute;
  left: calc(50% - 1396px);
  top: 40px;
  display: flex;
  align-items: center;
}

#header_week {
  position: absolute;
  right: 2%;
  top: 35px;
}
</style>