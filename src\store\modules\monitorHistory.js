/**
 * 监控视频播放历史记录管理模块
 */

const STORAGE_KEY = 'monitorHistoryList'
const MAX_HISTORY_COUNT = 10

const state = {
  // 海康威视视频播放历史记录
  hkVideoHistory: [],
  // 大华视频播放历史记录（预留）
  dhVideoHistory: []
}

const mutations = {
  // 设置海康威视视频历史记录
  SET_HK_VIDEO_HISTORY(state, historyList) {
    state.hkVideoHistory = historyList
  },

  // 添加海康威视视频到历史记录
  ADD_HK_VIDEO_TO_HISTORY(state, videoData) {
    // 检查是否已存在相同的记录（根据cameraIndexCode判断）
    const existingIndex = state.hkVideoHistory.findIndex(item => 
      item.cameraIndexCode === videoData.cameraIndexCode
    )

    // 如果已存在，先移除旧记录
    if (existingIndex !== -1) {
      state.hkVideoHistory.splice(existingIndex, 1)
    }

    // 创建历史记录项
    const historyItem = {
      id: videoData.id,
      name: videoData.name,
      cameraIndexCode: videoData.cameraIndexCode,
      resourceId: videoData.resourceId,
      resourceType: videoData.resourceType,
      nodeType: videoData.nodeType,
      parentId: videoData.parentId,
      timestamp: new Date().getTime(),
      type: 'hk' // 标记为海康威视视频
    }

    // 将新记录添加到数组开头
    state.hkVideoHistory.unshift(historyItem)

    // 限制历史记录最多保存指定条数
    if (state.hkVideoHistory.length > MAX_HISTORY_COUNT) {
      state.hkVideoHistory = state.hkVideoHistory.slice(0, MAX_HISTORY_COUNT)
    }
  },

  // 清空海康威视视频历史记录
  CLEAR_HK_VIDEO_HISTORY(state) {
    state.hkVideoHistory = []
  },

  // 移除指定的海康威视视频历史记录
  REMOVE_HK_VIDEO_FROM_HISTORY(state, cameraIndexCode) {
    const index = state.hkVideoHistory.findIndex(item => 
      item.cameraIndexCode === cameraIndexCode
    )
    if (index !== -1) {
      state.hkVideoHistory.splice(index, 1)
    }
  }
}

const actions = {
  // 初始化历史记录（从localStorage加载）
  initHistoryFromStorage({ commit }) {
    try {
      const historyData = localStorage.getItem(STORAGE_KEY)
      if (historyData) {
        const parsedData = JSON.parse(historyData)
        // 如果存储的是数组格式（兼容旧版本）
        if (Array.isArray(parsedData)) {
          commit('SET_HK_VIDEO_HISTORY', parsedData)
        } else if (parsedData.hkVideoHistory) {
          // 如果存储的是对象格式
          commit('SET_HK_VIDEO_HISTORY', parsedData.hkVideoHistory)
        }
      }
    } catch (error) {
      console.error('从localStorage加载监控历史记录失败:', error)
      commit('SET_HK_VIDEO_HISTORY', [])
    }
  },

  // 保存历史记录到localStorage
  saveHistoryToStorage({ state }) {
    try {
      const historyData = {
        hkVideoHistory: state.hkVideoHistory,
        dhVideoHistory: state.dhVideoHistory,
        lastUpdated: new Date().getTime()
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(historyData))
    } catch (error) {
      console.error('保存监控历史记录到localStorage失败:', error)
    }
  },

  // 添加海康威视视频到历史记录
  addHkVideoToHistory({ commit, dispatch }, videoData) {
    commit('ADD_HK_VIDEO_TO_HISTORY', videoData)
    // 同步保存到localStorage
    dispatch('saveHistoryToStorage')
  },

  // 清空海康威视视频历史记录
  clearHkVideoHistory({ commit, dispatch }) {
    commit('CLEAR_HK_VIDEO_HISTORY')
    // 同步保存到localStorage
    dispatch('saveHistoryToStorage')
  },

  // 移除指定的海康威视视频历史记录
  removeHkVideoFromHistory({ commit, dispatch }, cameraIndexCode) {
    commit('REMOVE_HK_VIDEO_FROM_HISTORY', cameraIndexCode)
    // 同步保存到localStorage
    dispatch('saveHistoryToStorage')
  }
}

const getters = {
  // 获取海康威视视频历史记录
  hkVideoHistory: state => state.hkVideoHistory,

  // 获取最近播放的海康威视视频
  latestHkVideo: state => {
    return state.hkVideoHistory.length > 0 ? state.hkVideoHistory[0] : null
  },

  // 根据cameraIndexCode查找海康威视视频历史记录
  getHkVideoByCode: state => cameraIndexCode => {
    return state.hkVideoHistory.find(item => item.cameraIndexCode === cameraIndexCode)
  },

  // 获取历史记录总数
  hkVideoHistoryCount: state => state.hkVideoHistory.length
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
