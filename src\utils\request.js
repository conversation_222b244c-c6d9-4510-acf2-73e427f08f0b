import axios from 'axios';
import { MessageBox } from 'element-ui';
import { getToken,removeToken } from '@/utils/auth'

const METHOD = {
  GET: 'get',
  POST: 'post',
  PUT: 'put',
  DELETE: 'delete'
}

// 创建 axios 实例
const service = axios.create({
  timeout: 60000,
  withCredentials: true
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
      config.headers['Content-Type'] = "application/json;charset=UTF-8";
      config.headers['ptid'] = "PT0001";
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    // 这里可以根据后端约定的状态码进行统一处理
    if (res.code !== 200) {
      // 处理特定错误码
      if (res.code === 401) {
        MessageBox.alert('登录已过期，请重新登录', '提示', {
          confirmButtonText: '确定',
          callback: () => {
            removeToken()
            process.env.NODE_ENV === 'production'?window.location.href = '/jsc/#/login':window.location.href = '/#/login';
          }
        });
        return Promise.reject(new Error('token已过期,请重新登录'))
      }
      return res;
    }
    return res;
  },
  error => {
    console.error('响应错误:', error);
    return Promise.reject(error);
  }
);

async function request(data) {
  const url = `${process.env.VUE_APP_BASE_API}${data.url}`;

  try {
    let response;
    switch (data.method) {
      case METHOD.GET:
        response = await service.get(url, { params: data.params, headers: data.headers });
        break;
      case METHOD.POST:
        response = await service.post(url, data.data, { headers: data.headers });
        break;
      case METHOD.PUT:
        response = await service.put(url, data.data, { headers: data.headers });
        break;
      case METHOD.DELETE:
        response = await service.delete(url, { params: data.params, headers: data.headers });
        break;
      default:
        throw new Error(`不支持的请求方法: ${data.method}`);
    }

    if (response.code !== 200) {
      console.warn('请求返回非200状态:', {
        url,
        code: response.code,
        message: response.message
      });
    }

    return response;
  } catch (error) {
    const errorInfo = {
      status: error.response?.status || 500,
      message: error.message || '未知错误',
      url: url,
      params: data.params || data.data
    };
    console.error('请求失败:', errorInfo);
    throw errorInfo;
  }
}
async function requestAdm(data) {
  const url = `${process.env.VUE_APP_ADM_API}${data.url}`;

  try {
    let response;
    switch (data.method) {
      case METHOD.GET:
        response = await service.get(url, { params: data.params, headers: data.headers });
        break;
      case METHOD.POST:
        response = await service.post(url, data.data, { headers: data.headers });
        break;
      case METHOD.PUT:
        response = await service.put(url, data.data, { headers: data.headers });
        break;
      case METHOD.DELETE:
        response = await service.delete(url, { params: data.params, headers: data.headers });
        break;
      default:
        throw new Error(`不支持的请求方法: ${data.method}`);
    }

    if (response.code !== 200) {
      console.warn('请求返回非200状态:', {
        url,
        code: response.code,
        message: response.message
      });
    }

    return response;
  } catch (error) {
    const errorInfo = {
      status: error.response?.status || 500,
      message: error.message || '未知错误',
      url: url,
      params: data.params || data.data
    };
    console.error('请求失败:', errorInfo);
    throw errorInfo;
  }
}
async function requestZfjly(data) {
  const url = `${data.url}`;

  try {
    let response;
    switch (data.method) {
      case METHOD.GET:
        response = await service.get(url, { params: data.params, headers: data.headers });
        break;
      case METHOD.POST:
        response = await service.post(url, data.data, { headers: data.headers });
        break;
      case METHOD.PUT:
        response = await service.put(url, data.data, { headers: data.headers });
        break;
      case METHOD.DELETE:
        response = await service.delete(url, { params: data.params, headers: data.headers });
        break;
      default:
        throw new Error(`不支持的请求方法: ${data.method}`);
    }

    if (response.code !== 200) {
      console.warn('请求返回非200状态:', {
        url,
        code: response.code,
        message: response.message
      });
    }

    return {
      data: response
    };
  } catch (error) {
    const errorInfo = {
      status: error.response?.status || 500,
      message: error.message || '未知错误',
      url: url,
      params: data.params || data.data
    };
    console.error('请求失败:', errorInfo);
    throw errorInfo;
  }
}
// 第三方登录请求函数 - 专门处理外部API调用
async function requestThirdParty(config) {
  const { data, headers = {}, method = 'POST' } = config;
  const url = `${process.env.VUE_APP_BASE_API}${config.url}`;
  try {
    // 创建专门用于第三方请求的axios实例，不使用全局拦截器
    const thirdPartyService = axios.create({
      timeout: 60000,
      withCredentials: false // 第三方请求通常不需要携带凭证
    });

    // 准备form-data格式的数据
    const formData = new URLSearchParams();
    if (data) {
      Object.keys(data).forEach(key => {
        formData.append(key, data[key]);
      });
    }

    // 设置请求配置
    const requestConfig = {
      method: method.toLowerCase(),
      url: url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...headers
      },
      data: formData
    };

    const response = await thirdPartyService(requestConfig);

    // 返回响应数据
    return response.data;
  } catch (error) {
    const errorInfo = {
      status: error.response?.status || 500,
      message: error.message || '第三方接口请求失败',
      url: url,
      data: data
    };
    console.error('第三方接口请求失败:', errorInfo);
    throw errorInfo;
  }
}

export {
  request,
  requestAdm,
  requestThirdParty,
  requestZfjly
}