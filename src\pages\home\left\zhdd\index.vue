<template>
  <div>
    <CommonTitle text="指挥调度" @click.native="$router.push('/zhdd')"></CommonTitle>
    <div class="wrap-container">
      <CommonTitle2 text="今日值班" manageUrl="http://10.45.13.116/ygf/industryApp/xzzfzhzx/dutyToday"></CommonTitle2>
      <!-- 今日值班 -->
      <div class="zhtx_box box">
        <el-carousel
          :interval="5000"
          height="315px"
          type="card"
          arrow="always"
          indicator-position="outside"
          :autoplay="true"
          class="duty-carousel"
        >
          <!-- 总指挥长 -->
          <el-carousel-item v-if="zbList.length > 0">
            <div class="carousel-item duty-card">
              <div class="duty-avatar" v-if="zbList[0].img">
                <el-image :src="zbList[0].img" fit="cover">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="duty-avatar default-avatar" v-else></div>
              <div class="duty-info">
                <!--                <div class="duty-title">{{zbList[0].pos}}</div>-->
                <div class="info-item">
                  <span class="label">{{ zbList[0].pos }}：</span>
                  <span class="value">{{ zbList[0].name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">电话：</span>
                  <span class="value">{{ toPhone(zbList[0].phone) }}</span>
                </div>
                <div class="contact-icons">
                  <span class="Phoneicon" @click="zbList.length > 0 ? openCall(zbList[0].phone) : null"></span>
                  <span class="Videoicon" @click="zbList.length > 0 ? openVideo(zbList[0].phone) : null"></span>
                </div>
              </div>
            </div>
          </el-carousel-item>

          <!-- 其他值班人员 -->
          <el-carousel-item v-for="(item, index) in zbList.slice(1)" :key="index">
            <div class="carousel-item duty-card">
              <div class="duty-avatar" v-if='item.img'>
                <el-image :src="item.img" fit="cover">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="duty-avatar default-avatar" v-else></div>
              <div class="duty-info">
                <!--                <div class="duty-title">{{item.pos}}</div>-->
                <div class="info-item">
                  <span class="label">{{ item.pos }}：</span>
                  <span class="value">{{ item.name }}</span>
                </div>
                <div class="info-item">
                  <span class="label">电话：</span>
                  <span class="value">{{ toPhone(item.phone) }}</span>
                </div>
                <div class="contact-icons">
                  <span class="Phoneicon" @click="openCall(item.phone)"></span>
                  <span class="Videoicon" @click="openVideo(item.phone)"></span>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <CommonTitle2 text="配套资源" manageUrl="https://csdn.dsjj.jinhua.gov.cn:8303/lawEquip"></CommonTitle2>
      <!-- 配套资源 -->
      <div class="ptzy_box box">
        <div class="resource-item" v-for="(item, index) in ptzyList" :key="index">
          <div class="resource-label">{{ item.name }}</div>
          <div class="resource-icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="resource-value">
            <!--            <span class="value-num" :class="index%3==1?'blue-text':'yellow-text'">{{item.value}}</span>-->
            <!--            <span class="value-unit" :class="index%3==1?'blue-text':'yellow-text'">{{item.unit}}</span>-->

            <span class="value-num yellow-text">{{ item.value }}</span>
            <span class="value-unit yellow-text">{{ item.unit }}</span>
          </div>
        </div>
      </div>
      <div class="base"></div>
    </div>
    <!-- 通话管理器组件 -->
    <CallManager
      ref="callManager"
      :config="callConfig"
      @initialized="onCallManagerInitialized"
      @error="onCallManagerError"
      @call-ended="onCallEnded"
      @signed-out="onSignedOut"
    />
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import CallManager from '@/components/CallManager'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
    CallManager,
  },
  data() {
    return {
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      zbList: [],
      ptzyList: [
        {
          icon: require('@/assets/common/ptzy1.png'),
          name: '执法人员',
          value: 333,
          unit: '人',
        },
        {
          icon: require('@/assets/common/ptzy2.png'),
          name: '机动车辆',
          value: 100,
          unit: '辆',
        },
        {
          icon: require('@/assets/common/ptzy3.png'),
          name: '非机动车辆',
          value: 56,
          unit: '辆',
        },
        {
          icon: require('@/assets/common/ptzy4.png'),
          name: '执法记录仪',
          value: 4307,
          unit: '个',
        },
        {
          icon: require('@/assets/common/ptzy5.png'),
          name: '对讲机',
          value: 113,
          unit: '个',
        },
        {
          icon: require('@/assets/common/ptzy6.png'),
          name: 'PDA',
          value: 98,
          unit: '个',
        },
      ],
      // 通话配置
      callConfig: {
        tenantType: 1,
        ip: 'ygf.xzzfj.jinhua.gov.cn',
        port: '443',
        vccId: '100317',
        agentId: '1001',
        password: 'Zyzx@10086',
        loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
      },
    }
  },
  computed: {},
  beforeDestroy() {
    // CallManager组件会自动处理清理工作
  },
  created() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initApi(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.initApi(localStorage.getItem('city'), year)
    })
    this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
  },
  methods: {
    // 通话管理器事件处理
    onCallManagerInitialized() {
      console.log('通话管理器初始化成功')
    },

    onCallManagerError(error) {
      console.error('通话管理器错误:', error)
      this.$message.error('通话系统初始化失败')
    },

    onCallEnded() {
      console.log('通话已结束')
    },

    onSignedOut() {
      console.log('已退出通话系统')
    },

    // 发起语音通话
    openCall(phone) {
      if (this.$refs.callManager) {
        this.$refs.callManager.makeVoiceCall(phone)
      }
    },

    // 发起视频通话
    openVideo(phone) {
      if (this.$refs.callManager) {
        this.$refs.callManager.makeVideoCall(phone)
      }
    },
    initApi(city, year) {
      const areaName = city === '金华开发区' ? '开发区' : city

      // 使用 Promise.all 来并行处理 API 请求
      Promise.all([
        indexApi('/csdn_yjyp13new', { area_name: areaName }),
        indexApi('/csdn_yjyp12', { area_name: city }),
        indexApi('/csdn_yjyp26', { area_code: city }),
      ]).then(([response1, response2, response3]) => {
        let res1 = response1.data
        this.zbList = [
          {
            pos: '总指挥长',
            name: res1[0]?.zzhzxm || '暂无数据',
            phone: res1[0]?.zzhzdh || '暂无数据',
            img: res1[0]?.zzhztx ? this.subUrl(res1[0].zzhztx) : '',
          },
          {
            pos: '值班领导',
            name: res1[0]?.zbldxm || '暂无数据',
            phone: res1[0]?.zblddh || '暂无数据',
            img: res1[0]?.zbldtx ? this.subUrl(res1[0].zbldtx) : '',
          },
          {
            pos: '值班长',
            name: res1[0]?.zbzxm || '暂无数据',
            phone: res1[0]?.zbzdh || '暂无数据',
            img: res1[0]?.zbztx ? this.subUrl(res1[0].zbztx) : '',
          },
        ]

        // 更新 ptzyList
        this.ptzyList[0].value = response3.data[0].tjz
        this.ptzyList[1].value = response2.data[0].jdcl
        this.ptzyList[2].value = response2.data[0].fjdcl
        this.ptzyList[3].value = response2.data[0].jlyzx
        this.ptzyList[4].value = response2.data[0].djj
        this.ptzyList[5].value = response2.data[0].pda
      })
    },
    subUrl(url) {
      return url.replace('8303', '8300')
    },
    toPhone(phone) {
      var reg = /(\d{3})\d{4}(\d{4})/ //正则表达式
      return phone.replace(reg, '$1****$2')
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.wrap-container {
  width: 100%;
  height: fit-content;
  margin-bottom: 40px;
  .box {
    padding: 20px 40px;
    box-sizing: border-box;
    position: relative;
  }
  .zhtx_box {
    padding: 10px 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .ptzy_box {
    width: 100%;
    height: 180px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
  }
  .base {
    width: 100%;
    height: 50px;
    background: url('@/assets/common/base.png') no-repeat;
    background-size: 100%;
    position: relative;
    bottom: 30px;
    z-index: 2;
  }
  .resource-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 16.66%;
    position: relative;
    .resource-icon {
      width: 150px;
      height: 120px;
      margin-bottom: 10px;
      position: relative;
      top: 10px;
      z-index: 3;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .resource-label {
      font-size: 27px;
      color: #ffffff;
      margin-top: 60px;
    }

    .resource-value {
      display: flex;
      align-items: baseline;
      position: relative;
      top: 20px;

      .value-num {
        font-size: 42px;
        font-family: DINCondensed;
        //font-style: italic;
        font-weight: bold;
      }

      .value-unit {
        font-size: 18px;
        margin-left: 5px;
      }

      .yellow-text {
        background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
        -webkit-background-clip: text;
        color: transparent;
      }

      .blue-text {
        color: #34dfe3;
      }
    }
  }

  .s-yellow {
    color: #eed252;
  }
  .s-blue {
    color: #34dfe3;
  }
  .item_name {
    width: 200px;
    height: 35px;
    font-size: 28px;
    line-height: 35px;
    color: #d1d6df;
  }
  .xt_font {
    font-family: DINCondensed;
    font-style: italic;
  }

  // 轮播样式 - 参考SmartScheduling.vue
  .duty-carousel {
    width: 800px;

    /deep/ .el-carousel__container {
      width: 100%;
    }

    /deep/ .el-carousel__indicators {
      bottom: -10px;
    }

    /deep/ .el-carousel__indicator {
      .el-carousel__button {
        background-color: rgba(0, 234, 255, 0.3);
      }

      &.is-active {
        .el-carousel__button {
          background-color: #00eaff;
        }
      }
    }

    /deep/ .el-carousel__arrow {
      background-color: rgba(0, 106, 220, 0.7);
      font-size: 16px;

      &:hover {
        background-color: rgba(0, 180, 255, 0.9);
      }
    }

    /deep/ .el-carousel__item {
      border-radius: 8px;
      overflow: visible;

      &.is-active {
        .duty-card {
          transform: scale(1.05);
          border-top: 2px solid #00eaff;
          background: url('@/assets/images/cardBg.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    /deep/ .el-carousel__item:not(.is-active) {
      transform: scale(0.85);
      opacity: 0.7;
    }
  }

  .carousel-item {
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .duty-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: url('@/assets/images/cardBg.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 8px;
    padding: 15px;
    box-sizing: border-box;
  }

  .duty-avatar {
    width: 120px;
    height: 150px;
    border-radius: 8px;
    margin-bottom: 15px;
    margin-top: 15px;
    overflow: hidden;
    border: 2px solid rgba(0, 234, 255, 0.5);

    .el-image {
      width: 100%;
      height: 100%;
    }
  }

  .default-avatar {
    background: url('@/assets/images/avatar.png') no-repeat center;
    background-size: cover;
    background-color: rgba(0, 106, 220, 0.7);
  }

  .duty-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .duty-title {
      font-size: 40px;
      font-weight: bold;
      background: linear-gradient(to bottom, #7cb2ff, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      margin-bottom: 15px;
      text-shadow: 0 0 10px rgba(124, 178, 255, 0.5);
    }

    .info-item {
      font-size: 28px;
      color: #eeeaea;
      margin-bottom: 10px;

      .label {
        color: #d1d6df;
      }

      .value {
        margin-left: 10px;
        color: #ffffff;
      }
    }
  }

  .contact-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 5px;
    margin-bottom: 15px;
  }

  .Phoneicon,
  .Videoicon {
    display: inline-block;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .Phoneicon {
    background: url('@/assets/images/phone-icon.png') no-repeat;
    background-size: contain;
    &:hover {
      transform: scale(1.1);
      filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.8));
    }
  }

  .Videoicon {
    background: url('@/assets/images/video-icon.png') no-repeat;
    background-size: contain;
    &:hover {
      transform: scale(1.1);
      filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.8));
    }
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 106, 220, 0.7);
    color: #ffffff;
    font-size: 25px;

    i {
      font-size: 40px;
    }
  }
}
// 通话相关样式已移至独立的通话组件中
</style>