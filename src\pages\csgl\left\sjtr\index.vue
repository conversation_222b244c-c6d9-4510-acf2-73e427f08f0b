<template>
  <div>
    <CommonTitle text="事件统览"></CommonTitle>
    <div class="tab_box">
      <div>
        <el-date-picker
          v-model="datas"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="queryData"
          :append-to-body="false"
        ></el-date-picker>
      </div>
      <div class="qy_box">
        <span class="txt">区域范围</span>
        <el-select
          v-model="value"
          placeholder="请选择"
          style="width: 220px; position: relative; right: 20px"
          @change="queryData"
        >
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
    <div class="list_box">
      <div class="list_item" v-for="(item, index) in listData" :key="index" @click="makePhone">
        <img :src="item.icon" alt="" />
        <div class="count_box">
          <div class="name">{{ item.name }}</div>
          <div class="line"></div>
          <div class="num_box">
            <div class="num">{{ item.count }}</div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </div>
      </div>
    </div>
    <CommonTitle2 text="高发事件/部件统计Top榜"></CommonTitle2>
    <div class="wrap-container" ref="chart"></div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import { getEventOverview, getEventTop } from '@/api/csgl/index.js'
import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2,
  },
  data() {
    return {
      datas: [new Date().getFullYear() + '-01-01', moment(new Date()).format('YYYY-MM-DD')],
      value: '',
      options: [
        { label: '金华市', value: '' },
        { label: '婺城区', value: '婺城区' },
        { label: '金东区', value: '金东区' },
        { label: '兰溪市', value: '兰溪市' },
        { label: '东阳市', value: '东阳市' },
        { label: '义乌市', value: '义乌市' },
        { label: '永康市', value: '永康市' },
        { label: '浦江县', value: '浦江县' },
        { label: '武义县', value: '武义县' },
        { label: '磐安县', value: '磐安县' },
        { label: '开发区', value: '开发区' },
      ],
      listData: [
        { name: '上报事件数', count: 2254, icon: require('@/assets/csgl/sjtr_icon1.png'), unit: '件' },
        { name: '立案事件数', count: 2254, icon: require('@/assets/csgl/sjtr_icon2.png'), unit: '件' },
        { name: '立案率', count: 2254, icon: require('@/assets/csgl/sjtr_icon3.png'), unit: '%' },
        { name: '事件处置数', count: 2254, icon: require('@/assets/csgl/sjtr_icon4.png'), unit: '件' },
        { name: '事件结案数', count: 2254, icon: require('@/assets/csgl/sjtr_icon5.png'), unit: '件' },
        { name: '结案率', count: 2254, icon: require('@/assets/csgl/sjtr_icon6.png'), unit: '%' },
      ],
      chartsData: [],
      chartTotal: '',
    }
  },
  computed: {},
  mounted() {
    this.init()
  },
  methods: {
    makePhone() {
      // this.$emit('makePhone')
    },
    queryData() {
      this.init()
    },
    init() {
      this.getData()
      this.getData1()
    },
    getData() {
      let params = {}
      if (this.datas && this.datas.length > 0) {
        params.startTime = this.datas[0]
        params.endTime = this.datas[1]
      } else {
        params.startTime = ''
        params.endTime = ''
      }
      params.area = this.value
      getEventOverview(params).then((res) => {
        this.listData[0].count = res.data.reportCount
        this.listData[1].count = res.data.registerCount
        this.listData[2].count = res.data.registerPercent
        this.listData[3].count = res.data.handleCount
        this.listData[4].count = res.data.completeCount
        this.listData[5].count = res.data.completePercent
      })
    },
    getData1() {
      let params = {}
      if (this.datas && this.datas.length > 0) {
        params.startTime = this.datas[0]
        params.endTime = this.datas[1]
      } else {
        params.startTime = ''
        params.endTime = ''
      }
      params.area = this.value
      getEventTop(params).then((res) => {
        this.chartsData = res.data.topList.map((item) => {
          return {
            name: item.key,
            value: item.value,
          }
        })
        this.chartTotal = res.data.totalCount
        this.initChart()
      })
    },
    initChart() {
      this.chart = this.$echarts.init(this.$refs.chart)

      const option = {
        backgroundColor: 'transparent',
        graphic: {
          type: 'image',
          style: {
            image: require('@/assets/dog/chartBg.png'),
            width: 360,
            height: 360,
          },
          left: '7.5%', // 调整背景图位置，与环形图对齐
          top: 'center',
        },
        title: [
          {
            text: this.chartTotal,
            left: '20%',
            top: '48%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 52,
              fontWeight: 'normal',
              fontFamily: 'DIN',
              lineHeight: 72,
            },
            z: 10, // 确保文字在背景图之上
          },
          {
            text: '总数量',
            left: '20%',
            top: '38%',
            textStyle: {
              color: '#E3F4FF',
              fontSize: 28,
              fontWeight: 'normal',
              lineHeight: 28,
            },
            z: 10, // 确保文字在背景图之上
          },
        ],
        legend: {
          orient: 'vertical',
          left: '55%',
          y: 'center',
          itemWidth: 16,
          itemHeight: 16,
          itemGap: 28,
          icon: 'circle',
          formatter: (name) => {
            var data = option.series[0].data //获取series中的data
            let tarValue = 0
            var total = 0
            for (var i = 0, l = data.length; i < l; i++) {
              total += Number(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            let percentage = ((tarValue / total) * 100).toFixed(2)
            return `{name|${name}}{percent|${percentage}%}{value|${tarValue}}`
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
              },
              value: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 0, 0, 0],
              },
              percent: {
                fontSize: 26,
                color: '#fff',
                padding: [0, 30, 0, 0],
              },
            },
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['65%', '75%'],
            center: ['25%', '50%'],
            startAngle: 90,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(2,47,115,0.5)',
              borderWidth: 2,
            },
            data: this.chartsData,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
          },
        ],
      }

      this.chart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
.tab_box {
  display: flex;
  align-items: center;
  align-content: center;
  margin: 20px 40px;
  .qy_box {
    margin-left: 48px;
    .txt {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 32px;
      margin-right: 48px;
    }
  }
}
/deep/ .el-input__inner {
  height: 48px !important;
  background-color: #132c4e !important;
  border: 2px solid #afdcfb !important;
  color: #fff !important;
  border-radius: 15px !important;
  font-size: 24px;
}
.list_box {
  display: flex;
  flex-wrap: wrap;
  margin-left: 70px;
  margin-bottom: 48px;
  .list_item {
    width: 50%;
    display: flex;
    align-content: center;
    align-items: center;
    margin-top: 48px;
    img {
      width: 99px;
      height: 99px;
    }
    .count_box {
      margin-left: 24px;
      .name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28px;
        color: #c1d3e3;
        line-height: 41px;
      }
      .line {
        width: 123px;
        height: 4px;
        background: linear-gradient(
          268deg,
          rgba(0, 121, 227, 0) 0%,
          rgba(70, 155, 227, 0.5882) 51%,
          rgba(8, 17, 26, 0) 100%
        );
        margin: 4px 0;
      }
      .num_box {
        display: flex;
        align-items: baseline;
        .num {
          font-family: DS-Digital, DS-Digital;
          font-weight: 700;
          font-size: 48px;
          color: #3fd9ff;
        }
        .unit {
          font-family: DS-Digital, DS-Digital;
          font-weight: 700;
          font-size: 24px;
          color: #c1d3e3;
        }
      }
    }
  }
}
.wrap-container {
  width: 100%;
  height: 480px;
  margin-bottom: 40px;
  position: relative;
}
</style>