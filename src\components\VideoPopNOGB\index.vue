<!--执法仪弹窗非国标7.0-->
<template>
  <ygfDialog :visible='visible' width='1500px' style='margin-top: 100px;'>
    <div class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 font-syBold dialogTitle">{{ title }}</div>
        <div class="close" @click="closeVideo"></div>
      </div>
      <video
        ref="video"
        controls="controls"
        autoplay="autoplay"
        muted="false"
        style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
      ></video>
      <div
        class="control-panel"
        style="
          z-index: 1000;
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 10px;
          border-radius: 5px;
          gap: 10px;
        "
      >
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="startVideo"
        >
          播放视频
        </button>
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="toggleAudio"
        >
          {{ isAudio ? '关闭对讲' : '开启对讲' }}
        </button>
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="stopAll"
        >
          停止
        </button>
      </div>
      <div
        class="status"
        ref="statusText"
        style="
          position: absolute;
          top: 60px;
          left: 20px;
          color: #fff;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 5px 10px;
          border-radius: 3px;
          display: none;
        "
      ></div>
      <div
        class="loading"
        ref="loading"
        style="
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 18px;
          display: none;
        "
      >
        正在加载，请稍候...
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { hex_md5 } from './md5.js'
import { getkey, online, get_info, login, startLive, stopLive, startAudio, stopAudio, send_cmd, gdlist_dv, gdlist, get_point } from '@/api/zfjlyApi'
export default {
  name: "index",
  components: {
    ygfDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    baseURL: {
      type: String,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    pwd: {
      type: String,
      required: true
    },
    videoCode: {
      type: String,
      default: "T0C2675"
    },
    title: {
      type: String,
      default: "执法记录仪"
    },
    type: {
      type: String,
      default: "openVideoTest" // 可选值: openVideoTest, ddhjVideoTest
    }
  },
  data() {
    return {
      player: null,
      isAudio: false,
      recorders: null,
      recordersPlatform: null,
      ws: null,
      timerId: null,
      dataInfo_set: [],
      form: {
        hostbodyArr: null,
        hostbody: null
      },
      dataInfo_hostbody: null,
      wsConnected: false,
      webRtcUrl: null,
      currentSn: null,
      currentHostbody: null,
      wsChannelId: null,
      sn: null,
      isVolumeOn: false,
      wsUrl: null,
      num: 0,
      pulldata: null,
      // 音频对讲相关属性 - 参考zfjly.js
      pcStartAudio: null,
      isStartAudio: 0,
      isC2: false
    };
  },
  created() {
    // 初始化时只执行一次，不再放在这里
  },
  mounted() {
    if (this.visible) {
      this.init();
    }
  },
  beforeDestroy() {
    this.closeAll();
  },
  methods: {
    async init() {
      this.showLoading(true);
      this.showStatus("正在连接执法记录仪平台...");

      this.currentHostbody = this.videoCode;

      // 登录系统
      const loginSuccess = await this.loginZFJLY();
      if (loginSuccess) {
        this.showStatus("已连接执法记录仪平台");
        this.wsConnected = true;

        // 获取设备信息
        await this.getDeviceInfo();
        this.showLoading(false);

        if (this.type === "openVideoTest") {
          this.startVideo();
        } else if (this.type === "ddhjVideoTest") {
          this.startVideo();
          setTimeout(() => {
            this.startAudio();
          }, 2000);
        }
      } else {
        this.showStatus("连接执法记录仪平台失败");
        this.showLoading(false);
      }
    },

    // 显示/隐藏加载状态
    showLoading(show) {
      this.$refs.loading.style.display = show ? "flex" : "none";
    },

    // 显示状态文本
    showStatus(text) {
      this.$refs.statusText.textContent = text;
      this.$refs.statusText.style.display = text ? "block" : "none";
      console.log("状态:", text);
    },
    hex_md5(str) {
      return hex_md5(str);
    },
    param_up(param_arr) {
      var keys = Object.keys(param_arr).sort();
      var string = "";
      for (var i = 0; i < keys.length; i++) {
        var k = keys[i];
        string += k + "=" + param_arr[k] + ";";
      }
      string += this.hex_md5("Pe2695jingyi");
      let str_encode = encodeURIComponent(string);
      param_arr.pe_signals = this.hex_md5(str_encode);
      return JSON.stringify(param_arr);
    },
    extendSignal(target) {
      let keys = Object.keys(target),
        arr = [],
        solt = "Pe2695jingyi",
        str,
        pe_signals;
      keys.sort();
      keys.forEach((key) => {
        const value = JSON.stringify(target[key]);
        arr.push(`${key}=${value}`);
      });
      str = arr.join(";") + this.hex_md5(solt);
      str = encodeURIComponent(str);
      pe_signals = this.hex_md5(str);
      target.pe_signals = pe_signals;
      return target;
    },
    closeVideo() {
      // 停止所有流和释放资源
      this.stopAll();
    },
    // 登录执法记录仪平台 - 参考zfjly.js实现
    async loginZFJLY() {
      return new Promise((resolve) => {
        this.getLoginBaseInfo("").then((res) => {
          const send = {
            username: this.username,
            pwd: this.pwd,
            token: res.data,
          };

          this.login(send).then((el) => {
            console.log(el,"zfyLogin")
            if (el.data.code === 200) {
              this.heartbeat().then(() => {
                this.getUserInformation().then((e) => {
                  try {
                    this.wsUrl = e.data.data.wsurl;
                    this.createWebSocket(e.data.data.wsurl, e.data.data);
                    resolve(true);
                  } catch (error) {
                    console.log("WebSocket创建失败:", error);
                    resolve(false);
                  }
                });
              });
            } else {
              resolve(false);
            }
          }).catch(error => {
            console.error("登录请求失败:", error);
            resolve(false);
          });
        }).catch(error => {
          console.error("获取登录基础信息失败:", error);
          resolve(false);
        });
      });
    },

    // 创建WebSocket连接 - 参考zfjly.js实现
    createWebSocket(wsUrl, userData) {
      const _this = this;
      try {
        this.ws = new WebSocket(wsUrl);
      } catch (error) {
        console.log("WebSocket创建失败:", error);
        return;
      }

      const data1 = {
        logincode: userData.logincode,
        username: userData.username,
        scode: userData.scode,
        cate: userData.auth_cate,
      };

      const psd = {
        command: "client_login",
        data: JSON.stringify(data1),
      };

      this.ws.onopen = function () {
        console.log("WebSocket已连接，发送登录信息");
        _this.ws.send(JSON.stringify(psd));
      };

      this.ws.onerror = function (e) {
        console.warn("WebSocket连接出错:", e);
        _this.ws.close();
        _this.ws = null;
      };

      this.ws.onclose = function () {
        console.log("WebSocket已断开连接");
      };

      this.ws.onmessage = function (event) {
        console.log("收到WebSocket消息:", event.data);
        try {
          const data = JSON.parse(event.data);

          // 处理site_data信息 - 参考zfjly.js的处理方式
          if (data.site_data) {
            const dataInfo = data.site_data;

            // 处理开始播放视频事件 - 参考zfjly.js实现
            if (dataInfo.start_live) {
              if (dataInfo.start_live.recorder_type == "2") {
                _this.isC2 = true;
              } else {
                _this.isC2 = false;
              }
              const ws = "ws://" + dataInfo.start_live.wsInfo.wsIp + ":" + dataInfo.start_live.wsInfo.wsPort;
              const viewId = dataInfo.start_live.wsInfo.wsViewId;
              _this.pullFlow_vms2(ws, viewId);
            }

            // 处理开始音频事件 - 参考zfjly.js实现
            if (dataInfo.start_audio) {
              _this.isAudio = true;
              const wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
              _this.wsChannelId = dataInfo.start_audio.wsInfo.wsChannelId;
              _this.sn = dataInfo.start_audio.wsInfo.sn;
              _this.voice_pull_vms2(wss, _this.wsChannelId);
            }
          }
        } catch (error) {
          console.error("解析WebSocket消息错误:", error);
        }
      };
    },
    // 登录方法 - 参考zfjly.js实现
    login(param) {
      const base64 = {
        encode: function (str) {
          return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_, hex) {
            return String.fromCharCode('0x' + hex);
          }));
        }
      };

      let { username, pwd, token, captcha_code } = param;
      let password = hex_md5(pwd);
      let login_if = base64.encode(JSON.stringify({ username, password }));
      let data = this.param_up({ login_info: login_if, token, captcha_code, 'withCredentials': true });
      return login(this.baseURL,data)
    },

    // 获取登录基础信息 - 参考zfjly.js实现
    getLoginBaseInfo(key) {
      return getkey(this.baseURL,key);
    },
    getUserInformation() {
      return get_info(this.baseURL);
    },
    // 心跳
    heartbeat() {
      return new Promise((resolve) => {
        this.heart();
        this.timerId = setInterval(this.heart, 20000);
        resolve();
      });
    },
    heart() {
      this.online().then((e) => {
        console.log("心跳请求成功");
      });
    },
    online() {
      return online(this.baseURL);
    },
    // 获取设备信息
    async getDeviceInfo() {
      try {
        const res = await this.unitEquipTree ("1001", "bh", "dname", false);
        var lineon = [];
        let info = {
          ids: []
        };

        res.data.data.forEach((item) => {
          if (item.lineon == 1) {
            this.dataInfo_set.push(item);
            lineon.push(item.hostbody);
            info.ids.push(item.hostbody);
          }
        });

        const posRes = await this.getPosition(info);
        let data = posRes.data.data;
        if (data && data.length > 0) {
          data.forEach((item) => {
            let lat = item.lat;
            let lng = item.lng;
            let name = item.name;
          });
        }

        this.dataInfo_hostbody = lineon.toString();
        this.form.hostbodyArr = this.dataInfo_hostbody;
        this.form.hostbody = this.videoCode;
        return this.dataInfo_set;
      } catch (error) {
        console.error("获取设备信息失败:", error);
        return [];
      }
    },
    unitEquipTree(id = '', bh = 'bh', text = 'dname', isNewapi = false) {
      let data = {
        "id": id,
        "bh": bh,
        "text": text
      };
      this.extendSignal(data);

      if (isNewapi) {
        return gdlist_dv(this.baseURL,data);
      } else {
        return gdlist(this.baseURL,data);
      }
    },
    // 获取设备经纬度信息
    getPosition(data) {
      this.extendSignal(data);
      return get_point(this.baseURL,data);
    },

    // 开始视频播放
    async startVideo() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在连接视频流...");

      try {
        const res = await this.startLive(this.currentHostbody);
        if (res.data.code == 200) {
          if (res.data.data[0].is_existed) {
            this.webRtcUrl = res.data.data[0]?.play_info?.webrtc_url || res.data.data[0].webrtc_url;

            // 保存sn值，用于停止视频流时使用
            if (res.data.data[0].sn) {
              this.currentSn = res.data.data[0].sn;
              console.log("获取到视频流SN:", this.currentSn);
            }

            this.showStatus("视频流连接成功，正在加载...");
            this.showLoading(false);

            // 使用WebRTC方式播放 - 参考video.html
            const result = await this.createWebRTCPlayer(this.$refs.video, this.webRtcUrl);
            if (result && result.type == true) {
              this.player = result.player;
              this.showStatus("视频播放中");
            } else {
              this.showStatus("视频播放失败");
            }
          } else {
            this.showStatus("设备不在线");
            this.showLoading(false);
          }
        } else {
          this.showStatus("无法连接设备: " + (res.data.msg || "未知错误"));
          this.showLoading(false);
        }
      } catch (err) {
        console.error("视频播放错误:", err);
        this.showStatus("视频播放错误: " + err);
        this.showLoading(false);
      }
    },

    // 开始拉流指定设备的视频 - 参考zfjly.js实现
    startLive(hostbody) {
      const send = {
        hostbody_arr: Array.isArray(hostbody) ? hostbody : [hostbody || "T0C0223"],
      };
      this.extendSignal(send);
      return startLive(this.baseURL,send);
    },

    // 切换音频对讲
    toggleAudio() {
      if (this.isAudio) {
        this.stopAudio();
      } else {
        this.startAudio();
      }
    },

    // 开始音频对讲 - 参考zfjly.js的startAudioIntercom实现
    async startAudio() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在建立对讲连接...");

      try {
        // 使用startAudioInVideo获取音频推流地址 - 参考zfjly.js
        const res = await this.startAudioInVideo(this.currentHostbody);

        if (res.code === 200 && res.data.platform_webrtc_push_url) {
          console.log("获取音频推流地址成功:", res.data.platform_webrtc_push_url);

          // 如果其他窗口推了麦克风的流，就不用走这里
          if (!this.isStartAudio) {
            const audioConfig = {
              element: "",
              debug: true,
              zlmsdpUrl: res.data.platform_webrtc_push_url,
              simulcast: false,
              useCamera: false,
              audioEnable: true,
              videoEnable: false,
              recvOnly: false,
              resolution: { w: 3840, h: 2160 },
              usedatachannel: false,
            };

            // 确保Webrtc类可用
            if (typeof Webrtc === 'undefined') {
              throw new Error("Webrtc类未加载");
            }

            this.pcStartAudio = new Webrtc(audioConfig);
            await this.pcStartAudio.start_play();

            this.isStartAudio++;
            console.log("音频推流启动成功，isStartAudio:", this.isStartAudio);

            // 监听连接失败事件
            this.pcStartAudio.on("failed", () => {
              console.log("音频推流网络断开");
              this.stopAudio();
            });
          } else {
            // 如果已经有音频推流，直接标记为活跃
            this.isStartAudio++;
            console.log("复用现有音频推流，isStartAudio:", this.isStartAudio);
          }

          this.isAudio = true;
          this.showStatus("对讲已开启");
          this.showLoading(false);
        } else {
          this.showStatus("设备不在线或不支持对讲");
          this.showLoading(false);
        }
      } catch (err) {
        console.error("对讲初始化失败:", err);
        this.showStatus("对讲初始化失败: " + err);
        this.showLoading(false);
      }
    },

    // 停止音频对讲 - 参考zfjly.js的stopAudioIntercom实现
    stopAudio() {
      if (!this.isAudio) return;

      this.showStatus("正在关闭对讲...");
      console.log("停止音频对讲:", this.currentHostbody);

      // 减少音频推流计数器
      this.isStartAudio--;

      // 如果计数器归零，关闭全局音频推流
      if (this.isStartAudio <= 0) {
        if (this.pcStartAudio) {
          try {
            this.pcStartAudio.close_play();
            this.pcStartAudio = null;
            console.log("全局音频推流已关闭");
          } catch (err) {
            console.error("关闭音频推流失败:", err);
          }
        }
        this.isStartAudio = 0; // 确保不会变成负数
      }

      // 调用停止音频接口
      if (this.currentHostbody) {
        this.stopAudioInVideo([this.currentHostbody]).then(() => {
          console.log("音频对讲接口已停止");
        }).catch(err => {
          console.error("停止音频对讲接口失败:", err);
        });
      }

      this.isAudio = false;
      this.showStatus("对讲已关闭");
      console.log("音频对讲已停止，当前isStartAudio:", this.isStartAudio);
    },

    // 初始化麦克风
    initMicrophone() {
      return new Promise((resolve, reject) => {
        // 导入音频处理模块
        import('./JY-chromePlayer.min.js').then(({ HZRecorder_pcm_push, HZRecorder_pcm }) => {
          navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

          if (!navigator.getUserMedia) {
            this.$message({
              message: "浏览器不支持音频输入",
              type: "warning"
            });
            reject("浏览器不支持音频输入");
            return;
          }

          navigator.getUserMedia(
            { audio: true },
            (stream) => {
              this.recordersPlatform = new HZRecorder_pcm_push(stream, {});
              this.recorders = new HZRecorder_pcm(stream, {});
              resolve();
            },
            (error) => {
              console.error("麦克风访问失败:", error);
              reject("麦克风访问失败: " + (error.name || error.message || "未知错误"));
            }
          );
        }).catch(err => {
          reject(err);
        });
      });
    },

    // 停止所有流
    stopAll() {
      this.showStatus("正在停止...");

      // 停止音频
      if (this.isAudio) {
        this.stopAudio();
      }

      // 停止视频流
      if (this.currentHostbody && this.currentSn) {
        // 调用接口停止视频流
        const stopData = {
          hostbody_arr: [this.currentHostbody],
          sn_arr: [this.currentSn]
        };

        console.log("调用stopLive接口停止视频流:", stopData);

        this.stopLive(stopData).then(res => {
          if (res.data && res.data.code == 200) {
            console.log("成功停止视频流");
            this.$emit('close');
          } else {
            console.warn("停止视频流失败:", res.data);
            this.$emit('close');
          }
        }).catch(err => {
          console.error("停止视频流接口调用失败:", err);
          this.$emit('close');
        });

        // 重置sn值
        this.currentSn = null;
      }

      // 停止视频播放器
      if (this.player) {
        // 使用WebRTC播放器的正确关闭方法
        if (typeof this.player.close_play === 'function') {
          this.player.close_play(this.$refs.video).then(() => {
            console.log("视频流已关闭");
          }).catch(err => {
            console.error("关闭视频流时出错:", err);
          });
        } else if (typeof this.player.close === 'function') {
          this.player.close();
          console.log("视频流已关闭");
        } else {
          console.warn("播放器没有有效的关闭方法");
          // 尝试直接清理视频元素
          if (this.$refs.video) {
            this.$refs.video.pause();
            this.$refs.video.srcObject = null;
            this.$refs.video.load();
          }
        }
        this.player = null;
      }

      setTimeout(() => {
        this.$emit('close');
      }, 1000);

      this.showStatus("已停止");
    },

    // 清理所有资源 - 参考zfjly.js的closeAll实现
    closeAll() {
      // 停止音频对讲
      if (this.isStartAudio > 0) {
        this.stopAudio();
      }

      // 停止音频推流
      if (this.pcStartAudio) {
        try {
          this.pcStartAudio.close_play();
          this.pcStartAudio = null;
          console.log("全局音频推流已关闭");
        } catch (err) {
          console.error("关闭音频推流失败:", err);
        }
      }

      if (this.player) {
        if (typeof this.player.stop === 'function') {
          this.player.stop();
        }
        this.player = null;
      }

      if (this.recorders) {
        this.recorders.closeWebsocket();
        this.recorders = null;
      }

      if (this.recordersPlatform) {
        this.recordersPlatform.closeWebsocket();
        this.recordersPlatform = null;
      }

      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }

      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      // 重置其他状态
      this.wsConnected = false;
      this.webRtcUrl = null;
      this.currentSn = null;
      this.currentHostbody = null;
      this.isAudio = false;
      this.dataInfo_set = [];
      this.form = {
        hostbodyArr: null,
        hostbody: null
      };
      this.dataInfo_hostbody = null;
      this.num = 0;
      this.sn = null;
      this.wsChannelId = null;
      this.isStartAudio = 0;
      this.isVolumeOn = false;
      this.wsUrl = null;
      this.pulldata = null;
      this.pcStartAudio = null;
    },

    startLiveVideo(data) {
      this.extendSignal(data);
      return startLive(this.baseURL,data);
    },

    startLiveAudio(data) {
      this.extendSignal(data);
      return startAudio(this.baseURL,data);
    },

    stopLive(data) {
      this.extendSignal(data);
      return stopLive(this.baseURL,data);
    },

    stopLiveAudio(data) {
      this.extendSignal(data);
      return stopAudio(this.baseURL,data);
    },

    send_cmd(data) {
      this.extendSignal(data);
      return send_cmd(this.baseURL,data);
    },

    // 音频对讲相关方法 - 参考zfjly.js实现
    startAudioInVideo(hostbody) {
      return new Promise((resolve, reject) => {
        let sent = {
          hostbody_arr: [hostbody],
        };

        // 使用startLiveAudio接口，适配当前系统
        this.startLiveAudio(sent).then((res) => {
          console.log("startAudioInVideo response:", res);
          if (res.data && res.data.code === 200 && res.data.data) {
            // 适配返回格式，添加platform_webrtc_push_url
            const audioData = res.data.data[0];
            if (audioData && audioData.platform_webrtc_push_url) {
              // 将webrtc_url作为platform_webrtc_push_url使用
              resolve({
                code: 200,
                data: {
                  platform_webrtc_push_url: audioData.platform_webrtc_push_url
                }
              });
            } else {
              reject("未获取到音频推流地址");
            }
          } else {
            reject("设备不在线或不支持对讲");
          }
        }).catch((err) => {
          console.error("startAudioInVideo error:", err);
          reject(err);
        });
      });
    },

    // 停止音频对讲
    stopAudioInVideo(hostbodyArr) {
      return new Promise((resolve) => {
        console.log("停止音频对讲:", hostbodyArr);
        // 调用stopLiveAudio接口
        this.stopLiveAudio({
          hostbody_arr: hostbodyArr
        }).then(() => {
          resolve();
        }).catch(() => {
          resolve(); // 即使失败也继续执行
        });
      });
    },

    // 创建WebRTC播放器并播放视频流 - 参考zfjly.js实现
    createWebRTCPlayer(videoElement, webRtcUrl) {
      if (!videoElement) {
        console.error("播放器元素不存在");
        return Promise.reject("播放器元素不存在");
      }

      return new Promise((resolve, reject) => {
        try {
          // 创建WebRTC播放器配置 - 参考zfjly.js的配置
          const webrtcConfig = {
            element: videoElement,
            debug: true,
            zlmsdpUrl: webRtcUrl,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 1280, h: 720 },
            usedatachannel: false
          };

          // 创建WebRTC播放器实例
          const webrtcPlayer = new Webrtc(webrtcConfig);

          // 开始播放
          webrtcPlayer.start_play().then(stream => {
            console.log("WebRTC播放成功:", stream);
            resolve({
              player: webrtcPlayer,
              stream: stream,
              type: true
            });
          }).catch(error => {
            console.error("WebRTC播放失败:", error);
            reject(error);
          });
        } catch (error) {
          console.error("创建WebRTC播放器失败:", error);
          reject(error);
        }
      });
    },

    // 拉流处理函数 - 参考zfjly.js和video.html实现
    pullFlow_vms2(ws, viewId) {
      console.log("pullFlow_vms2", ws, viewId);

      // 使用WebRTC播放
      this.createWebRTCPlayer(this.$refs.video, this.webRtcUrl).then((result) => {
        console.log("WebRTC拉流结果：", result);

        if (!result) {
          console.warn("WebRTC播放失败，可能连接异常");
          this.showStatus("拉流连接异常");
          return;
        }

        if (result.type == true) {
          this.$refs.video.classList.add("active");
          this.player = result.player;

          // 保存来自WebSocket的sn值，如果存在的话
          if (this.sn) {
            this.currentSn = this.sn;
            console.log("WebSocket消息触发的视频流，保存SN:", this.currentSn);
          }

          this.showStatus("视频播放中");
        } else {
          this.showStatus("拉流失败");
        }
      }).catch(error => {
        console.error("WebRTC拉流过程中发生错误：", error);
        this.showStatus("拉流失败，请检查连接");
      });
    },

    // 音频流处理 - 参考zfjly.js和video.html实现
    voice_pull_vms2(wss, wsChannelId) {
      console.log("voice_pull_vms2", wss, wsChannelId);

      // 如果使用了音频对讲功能，则不需要手动处理
      if (this.isStartAudio > 0) {
        console.log("使用音频对讲功能处理音频流");
        return;
      }

      // 兼容旧的实现方式
      if (this.recorders) {
        this.recorders.openWebSocket(wss, wsChannelId);
        this.isAudio = true;
        this.showStatus("对讲已开启");
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 确保组件每次显示时都重新初始化
        this.$nextTick(() => {
          this.init();
        });
      } else {
        this.closeAll();
      }
    }
  }
};
</script>

<style scoped>
.rwgz-tc {
  position: relative;
  width: 1500px;
  height: 1000px;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
  border-radius: 57px;
}

.rw-title {
  position: absolute;
  top: 50px;
  z-index: 888;
  border-radius: 57px 57px 0 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 1% 3%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
  cursor: pointer;
}

.btn:hover {
  background-color: #40a9ff !important;
}

.btn:disabled {
  background-color: #d9d9d9 !important;
  cursor: not-allowed;
}
</style>