.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 22px;
}
.calendar-title {
  text-align: center;
  height: 22px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 2px;
  padding: 0 3px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -7px;
  width: 14px;
  height: 14px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -18px -2px;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -34px -2px;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat -1px -2px;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -49px -2px;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.calendar-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 40px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  margin: 0;
  padding: 2px;
  font-weight: bold;
  font-size: 12px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 21px;
  height: 21px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 2px 2px;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -45px 2px;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #4d4d4d;
}
.calendar-day {
  color: #000000;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #D3D3D3;
}
.calendar {
  border-color: #D3D3D3;
}
.calendar-header {
  background: #f3f3f3;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #fafafa;
  padding: 2px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #e2e2e2;
  color: #000000;
}
.calendar-hover {
  border: 1px solid #ccc;
  padding: 0;
}
.calendar-selected {
  background-color: #0092DC;
  color: #fff;
  border: 1px solid #0070a9;
  padding: 0;
}
