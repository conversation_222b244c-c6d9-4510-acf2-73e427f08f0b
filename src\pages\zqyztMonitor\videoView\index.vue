<template>
  <div class="wrap-bg" style="height: 100%">
    <CommonTitle text="视频界面" style="margin-top: 40px" :styles="{ marginLeft: '140px' }"></CommonTitle>
    <div class="wrap-container">
      <div class="wrap-container-head">
        <div class="yearChange">
          <el-date-picker
            v-model="datas"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="queryData"
            :append-to-body="false"
            :picker-options="pickerOptions"
            popper-class="date-picker-up"
          ></el-date-picker>
        </div>
        <div class="wrap-container-tabs">
          <div
            class="wrap-container-tab"
            v-for="(item, i) in tabs"
            :key="i"
            :class="{ activeTab: i == currentTab }"
            @click="tabChange(i)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <!-- 主体内容区域 -->
      <div class="main-content-area">
        <!-- 大华视频显示区域 -->
        <template v-if="currentVideoType === 'dh'">
          <!-- 左侧视频区域 -->
          <div class="video-container">
            <!-- 大华视频组件 -->
            <VenueVideo
              ref="DWvideo"
              width="1500px"
              height="1054px"
              :visible="videoConfig.visible"
              :video-config="videoConfig"
            ></VenueVideo>
          </div>

          <!-- 右侧设备信息区域 -->
          <div class="device-info-container">
            <div class="info-section">
              <div class="info-title">设备信息</div>
              <div class="info-content">
                <div class="info-item">
                  <span class="info-label">视频系统:</span>
                  <span class="info-value video-system-badge" :class="currentVideoType">
                    {{ currentVideoType === 'dh' ? '大华视频' : '海康威视' }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">设备名称:</span>
                  <span class="info-value">{{ deviceInfo.name || '暂无数据' }}</span>
                </div>
                <div class="info-item" v-if="currentVideoType === 'hk' && hkVideoConfig.cameraIndexCode">
                  <span class="info-label">摄像头编码:</span>
                  <span class="info-value camera-index-code">{{ hkVideoConfig.cameraIndexCode }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">所属部门:</span>
                  <span class="info-value">{{ deviceInfo.department || '暂无数据' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">摄像头类型:</span>
                  <span class="info-value">{{ deviceInfo.cameraType || '暂无数据' }}</span>
                </div>
                <div class="info-item tag-item">
                  <span class="info-label">标签:</span>
                  <div class="tag-container">
                    <!-- 现有标签展示 -->
                    <span class="info-tag" v-for="(item, index) in deviceInfo.tags" :key="index">
                      {{ item.tag }}
                      <i class="el-icon-close tag-close" @click="removeTag(item.id, index)"></i>
                    </span>

                    <!-- 行内标签输入框 -->
                    <div v-if="showTagInput" class="inline-tag-input">
                      <el-input
                        v-model="newTagName"
                        size="small"
                        placeholder="输入标签"
                        @keyup.enter.native="addTag"
                        @blur="handleTagInputBlur"
                        ref="tagInput"
                      ></el-input>
                    </div>

                    <!-- 新增标签按钮 -->
                    <el-button
                      v-if="!showTagInput"
                      size="mini"
                      type="primary"
                      class="tag-button"
                      @click="showTagInputField"
                    >
                      自定义标签
                      <i class="el-icon-plus"></i>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧功能按钮区 -->
            <div class="function-buttons">
              <el-button type="primary" icon="el-icon-refresh-right" @click="refreshVideo">刷新</el-button>
              <el-button type="danger" icon="el-icon-close" @click="closeVideo">关闭</el-button>
            </div>
          </div>
        </template>

        <!-- 海康威视视频显示区域 -->
        <template v-if="currentVideoType === 'hk'">
          <div class="monitor-container">
            <div class="monitor-content">
              <div class="monitor-controls" style="display: none;">
                <el-button class="change-btn" type="text" @click="handleMonitorChangeMode">切换播放模式</el-button>
              </div>

              <!-- 视频播放区域 -->
              <div class="monitor-video">
                <videoBox ref="videoBox" :camera-index-code.sync="monitorCameraIndexCode" />
              </div>

              <!-- 播放记录 -->
              <div class="monitor-history">
                <div class="his-title">播放记录</div>
                <div class="his-list">
                  <div class="history-items" @wheel.prevent="handleHistoryScroll" ref="historyContainer">
                    <div
                      v-for="(item, idx) in monitorHistoryList"
                      :key="idx"
                      class="item"
                      @click="handleMonitorNodeClick(item)"
                    >
                      <div class="item-inner" />
                      <div class="item-name" :title="item.name">{{ item.name }}</div>
                    </div>
                    <div v-if="!monitorHistoryList.length" class="item empty-item">
                      <span>暂无记录</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import VenueVideo from '@/components/Video/VenueVideo'
import HKWSVideo from '@/components/HKWSVideo/index.vue'
import videoBox from '@/pages/board/views/components/videoBox/index.vue'
import moment from 'moment'
import { addVideoTag, deleteVideoTag } from '@/api/monitor/DWvideo'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'index',
  components: {
    CommonTitle,
    VenueVideo,
    HKWSVideo,
    videoBox,
  },
  data() {
    const today = moment().format('YYYY-MM-DD')
    const firstDay = moment().startOf('month').format('YYYY-MM-DD')

    return {
      currentTab: 0,
      tabs: [
        {
          name: '实时播放',
          value: 0,
        },
        {
          name: '录像回放',
          value: 1,
        },
      ],
      datas: [`${firstDay} 00:00:00`, `${today} 23:59:59`],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一小时',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      videoConfig: {},
      isVideoInitialized: false,

      // 视频类型控制
      currentVideoType: '', // 'dh' for 大华, 'hk' for 海康威视, '' for none

      // 海康威视视频配置
      hkVideoConfig: {
        cameraIndexCode: '',
        videoGrid: '1x1',
        title: '',
        name: '',
        resourceType: '',
        nodeType: '',
        parentId: '',
        playMode: 0, // 0-实时预览，1-录像回放
        timeRange: {
          startTime: '',
          endTime: '',
        },
      },
      isHkVideoInitialized: false,

      // 路由守卫引用
      routeGuard: null,

      // 设备信息
      deviceInfo: {
        name: '',
        department: '',
        cameraType: '',
        tags: [],
      },

      // 标签相关
      showTagInput: false,
      newTagName: '',
      currentDeviceId: null,

      // 监控页面相关数据
      monitorCameraIndexCode: '',

      // 用于强制重新计算样式的响应式属性
      scaleUpdateKey: 0,
    }
  },

  computed: {
    // 从store获取海康威视视频历史记录
    ...mapGetters('monitorHistory', ['hkVideoHistory', 'latestHkVideo', 'hkVideoHistoryCount']),

    // 兼容原有的monitorHistoryList命名
    monitorHistoryList() {
      return this.hkVideoHistory
    },
    // 监控视频区域动态样式
    monitorVideoStyle() {
      // 使用 scaleUpdateKey 确保响应式更新
      this.scaleUpdateKey

      const baseWidth = 1280
      const baseHeight = 490

      // 获取全局缩放比例
      const scaleWidth = window.scaleWidth || 1
      const scaleHeight = window.scaleHeight || 1

      // 根据缩放比例调整尺寸
      const adjustedWidth = baseWidth / scaleWidth
      const adjustedHeight = baseHeight / scaleHeight

      return {
        width: `${adjustedWidth}px`,
        height: `${adjustedHeight}px`,
        transform: `scale(${scaleWidth}, ${scaleHeight})`,
        transformOrigin: 'top left',
      }
    },

    // 播放记录区域动态样式
    monitorHistoryStyle() {
      // 使用 scaleUpdateKey 确保响应式更新
      this.scaleUpdateKey

      // 获取全局缩放比例
      const scaleHeight = window.scaleHeight || 1

      // 计算正确的 marginTop
      const marginTop = 490 * scaleHeight

      return {
        marginTop: `${marginTop}px`,
      }
    },
  },

  mounted() {
    // 从store初始化历史记录（会从localStorage加载）
    this.$store.dispatch('monitorHistory/initHistoryFromStorage')
    // 监听打开视频事件
    this.$bus.$on('openVideo', this.handleOpenVideo)

    // 监听打开海康威视视频事件
    this.$bus.$on('openHkVideo', this.handleOpenHkVideo)

    // 监听点击事件，用于处理标签输入框外部点击
    document.addEventListener('click', this.handleOutsideClick)

    // 添加路由守卫，确保页面离开时清理视频资源
    this.routeGuard = this.$router.beforeEach((to, from, next) => {
      // 检查是否正在离开当前页面
      if (from.path === this.$route.path && to.path !== this.$route.path) {
        console.log('检测到页面导航，准备清理视频资源')

        // 执行视频资源清理
        this.destroyAllVideos()
      }

      next()
    })

    // 添加浏览器关闭/刷新事件监听
    window.addEventListener('beforeunload', this.handleBeforeUnload)

    // 监听窗口大小变化，更新视频区域样式
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    console.log('videoView页面即将销毁，开始清理视频资源')

    // 移除事件监听
    this.$bus.$off('openVideo', this.handleOpenVideo)
    this.$bus.$off('openHkVideo', this.handleOpenHkVideo)
    document.removeEventListener('click', this.handleOutsideClick)
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
    window.removeEventListener('resize', this.handleWindowResize)

    // 移除路由守卫
    if (this.routeGuard) {
      this.routeGuard()
      this.routeGuard = null
    }

    // 销毁所有视频资源
    this.destroyAllVideos()
  },
  methods: {
    // 监控页面相关方法
    handleMonitorNodeClick(data) {
      console.log('handleMonitorNodeClick', data)
      if (data.type == 'hk') {
        this.monitorCameraIndexCode = data.cameraIndexCode

        // 通过store添加到播放记录（会自动同步到localStorage）
        const videoData = {
          ...data,
          id: data.id,
          name: data.name,
          cameraIndexCode: data.cameraIndexCode,
          parentId: data.parentId || '',
          type: data.type,
        }

        this.$store.dispatch('monitorHistory/addHkVideoToHistory', videoData)
      }
    },

    handleMonitorChangeMode() {
      if (this.$refs.videoBox) {
        this.$refs.videoBox.changePlayMode()
      }
    },

    // 处理播放记录横向滚动
    handleHistoryScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40
      const container = this.$refs.historyContainer
      if (container) {
        container.scrollLeft = container.scrollLeft - eventDelta / 4
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 延迟一点时间确保 window.scaleWidth 和 window.scaleHeight 已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.scaleUpdateKey++
        }, 100)
      })
    },

    // 处理打开视频事件
    handleOpenVideo(res) {
      console.log('打开大华视频:', res)

      // 确保切换到大华视频系统
      this.ensureVideoSystem('dh')

      // 设置视频配置
      this.videoConfig = {
        visible: true,
        domId: 'dom1',
        ctrlCode: 'ctrl',
        title: res.title || '监控视频',
        ctrlType: 'playerWin',
        ctrlProperty: {
          displayMode: 1,
          splitNum: 1,
          channelList: res.videoCode.split(',').map((item) => ({ channelId: item })),
        },
      }

      // 获取设备信息
      this.fetchDeviceInfo(res)
      this.currentDeviceId = res.id
      this.deviceCode = res.deviceCode

      // 初始化视频
      if (!this.isVideoInitialized) {
        this.$nextTick(() => {
          this.$refs.DWvideo.loginVideo()
          this.isVideoInitialized = true
        })
      } else {
        // 已初始化，只需切换视频源
        this.$nextTick(() => {
          this.$refs.DWvideo.createMinitor()
        })
      }
    },

    // 处理打开海康威视视频事件
    handleOpenHkVideo(res) {
      console.log('打开海康威视视频:', res)

      // 确保切换到海康威视视频系统
      this.ensureVideoSystem('hk')

      // 设置监控摄像头编码
      this.monitorCameraIndexCode = res.cameraIndexCode

      // 获取海康威视设备信息
      this.fetchHkDeviceInfo(res)
      this.currentDeviceId = res.id

      console.log('海康威视视频初始化完成')
    },

    // 获取海康威视设备信息
    fetchHkDeviceInfo(res) {
      // 海康威视设备信息映射
      const typeMap = {
        camera: '摄像头',
        organization: '组织',
      }

      this.deviceInfo = {
        name: res.name || '海康威视设备',
        department: res.parentId || '未知部门',
        cameraType: typeMap[res.resourceType] || '海康威视设备',
        tags: [], // 海康威视暂时不支持标签，可以后续扩展
      }
    },

    // 获取设备信息
    fetchDeviceInfo(res) {
      const typeMap = {
        1: '枪机',
        2: '球机',
        3: '半球等',
      }

      // 确保tags是数组
      const tags = Array.isArray(res.tags) ? res.tags : res.tags ? [res.tags] : []

      this.deviceInfo = {
        name: res.name,
        department: res.deptName,
        cameraType: typeMap[res.cameraType],
        tags: tags,
      }
    },

    // 查询数据（日期范围变化时）
    queryData() {
      // 根据当前标签页和日期范围查询数据
      if (this.currentTab === 1) {
        // 录像回放
        if (this.currentVideoType === 'dh') {
          this.loadVideoRecordings()
        } else if (this.currentVideoType === 'hk') {
          this.loadHkVideoRecordings()
        }
      }
    },

    // 标签页切换
    tabChange(i) {
      this.currentTab = i

      // 根据标签页类型调整视频行为
      if (i === 0) {
        // 实时播放
        if (this.currentVideoType === 'dh') {
          // 大华视频切换到实时模式
          if (this.videoConfig.ctrlCode && this.videoConfig.visible) {
            this.switchToLiveMode()
          }
        } else if (this.currentVideoType === 'hk') {
          // 海康威视切换到实时模式
          if (this.monitorCameraIndexCode && this.$refs.videoBox) {
            // videoBox组件会自动处理实时模式
            // console.log('海康威视切换到实时模式')
            this.handleMonitorChangeMode()
          }
        }
      } else if (i === 1) {
        // 录像回放
        if (this.currentVideoType === 'dh') {
          // 大华视频切换到回放模式
          if (this.videoConfig.ctrlCode && this.videoConfig.visible) {
            this.loadVideoRecordings()
          }
        } else if (this.currentVideoType === 'hk') {
          // 海康威视切换到回放模式
          if (this.monitorCameraIndexCode && this.$refs.videoBox) {
            // videoBox组件会自动处理回放模式
            console.log('海康威视切换到回放模式')
            this.handleMonitorChangeMode()
          }
        }
      }
    },

    // 切换到实时模式
    switchToLiveMode() {
      // 实时模式的设置
      this.videoConfig = {
        ...this.videoConfig,
        ctrlProperty: {
          ...this.videoConfig.ctrlProperty,
          displayMode: 1, // 实时模式
        },
      }

      // 刷新视频窗口
      this.$nextTick(() => {
        this.$refs.DWvideo.createMinitor()
      })
    },

    // 加载录像回放
    loadVideoRecordings() {
      if (!this.videoConfig.ctrlCode) return

      // 获取日期时间范围
      const startTime = this.datas[0]
      const endTime = this.datas[1]

      console.log('加载录像回放', { startTime, endTime })

      // 回放模式设置
      this.videoConfig = {
        ...this.videoConfig,
        ctrlProperty: {
          ...this.videoConfig.ctrlProperty,
          displayMode: 2, // 回放模式
          startTime,
          endTime,
        },
      }

      // 刷新视频窗口以应用新设置
      this.$nextTick(() => {
        this.$refs.DWvideo.createMinitor()
      })
    },

    // 刷新视频
    refreshVideo() {
      if (this.currentVideoType === 'dh') {
        // 大华视频刷新
        if (!this.videoConfig.ctrlCode) return
        this.$nextTick(() => {
          this.$refs.DWvideo.createMinitor()
        })
      } else if (this.currentVideoType === 'hk') {
        // 海康威视视频刷新
        if (!this.monitorCameraIndexCode) return

        // videoBox组件会自动处理视频刷新
        console.log('海康威视视频刷新')
      }

      this.$message.success('视频已刷新')
    },

    // 关闭视频
    closeVideo() {
      const currentSystem = this.currentVideoType

      if (currentSystem === 'dh') {
        // 关闭大华视频
        this.closeDhVideo()
      } else if (currentSystem === 'hk') {
        // 关闭海康威视视频
        this.closeHkVideo()
      }

      // 重置视频类型
      this.currentVideoType = ''

      // 发送视频关闭事件
      this.$bus &&
        this.$bus.$emit('videoSystemChanged', {
          from: currentSystem,
          to: '',
          timestamp: Date.now(),
        })

      this.$message.success('视频已关闭')
    },

    // 关闭大华视频（内部方法，不显示消息）
    closeDhVideo() {
      console.log('关闭大华视频')
      if (this.isVideoInitialized && this.$refs.DWvideo) {
        // 隐藏大华视频
        this.videoConfig.visible = false
        this.$nextTick(() => {
          this.$refs.DWvideo.changeVisible()
        })

        // 重置大华视频配置
        this.videoConfig = {}
        this.currentDeviceId = null
        this.deviceCode = null

        // 清空设备信息
        this.deviceInfo = {
          name: '',
          department: '',
          cameraType: '',
          tags: [],
        }

        console.log('大华视频已关闭')
      }
    },

    // 关闭海康威视视频（内部方法，不显示消息）
    closeHkVideo() {
      console.log('关闭海康威视视频')

      // 重置监控摄像头编码
      this.monitorCameraIndexCode = ''
      this.currentDeviceId = null

      // 清空设备信息
      this.deviceInfo = {
        name: '',
        department: '',
        cameraType: '',
        tags: [],
      }

      console.log('海康威视视频已关闭')
    },

    // 切换视频系统（统一处理）
    switchVideoSystem(targetSystem) {
      console.log(`切换视频系统到: ${targetSystem}`)

      if (this.currentVideoType === targetSystem) {
        console.log('已经是目标视频系统，无需切换')
        return
      }

      const fromSystem = this.currentVideoType

      // 关闭当前视频系统
      if (fromSystem === 'dh') {
        this.closeDhVideo()
      } else if (fromSystem === 'hk') {
        this.closeHkVideo()
      }

      // 切换到目标系统
      this.currentVideoType = targetSystem

      // 发送视频系统切换事件
      this.$bus &&
        this.$bus.$emit('videoSystemChanged', {
          from: fromSystem,
          to: targetSystem,
          timestamp: Date.now(),
        })

      console.log(`视频系统已切换: ${fromSystem} -> ${targetSystem}`)
    },

    // 检查并切换视频系统（如果需要）
    ensureVideoSystem(targetSystem) {
      if (this.currentVideoType !== targetSystem) {
        this.switchVideoSystem(targetSystem)
        return true // 表示进行了切换
      }
      return false // 表示无需切换
    },

    // 销毁所有视频资源（页面离开时调用）
    destroyAllVideos() {
      console.log('开始销毁所有视频资源')

      // 销毁大华视频
      this.destroyDhVideo()

      // 销毁海康威视视频
      this.destroyHkVideo()

      // 重置所有状态
      this.resetAllVideoStates()

      // 发送视频系统销毁事件
      this.$bus &&
        this.$bus.$emit('videoSystemDestroyed', {
          timestamp: Date.now(),
          destroyedSystems: ['dh', 'hk'],
        })

      console.log('所有视频资源销毁完成')
    },

    // 销毁大华视频
    destroyDhVideo() {
      console.log('销毁大华视频资源')

      try {
        if (this.isVideoInitialized && this.$refs.DWvideo) {
          // 登出大华视频
          this.$refs.DWvideo.logOut()
          console.log('大华视频已登出')

          // 如果有其他清理方法，也可以调用
          if (this.$refs.DWvideo.destroy) {
            this.$refs.DWvideo.destroy()
          }
        }

        // 重置大华视频相关状态
        this.videoConfig = {}
        this.isVideoInitialized = false
        this.deviceCode = null

        console.log('大华视频资源销毁完成')
      } catch (error) {
        console.error('销毁大华视频时发生错误:', error)
      }
    },

    // 销毁海康威视视频
    destroyHkVideo() {
      console.log('销毁海康威视视频资源')

      try {
        // 重置监控摄像头编码
        this.monitorCameraIndexCode = ''

        console.log('海康威视视频资源销毁完成')
      } catch (error) {
        console.error('销毁海康威视视频时发生错误:', error)
      }
    },

    // 重置所有视频状态
    resetAllVideoStates() {
      console.log('重置所有视频状态')

      // 重置通用状态
      this.currentVideoType = ''
      this.currentDeviceId = null
      this.currentTab = 0

      // 重置设备信息
      this.deviceInfo = {
        name: '',
        department: '',
        cameraType: '',
        tags: [],
      }

      // 重置标签相关状态
      this.showTagInput = false
      this.newTagName = ''
      this.editingTag = null
      this.editingTagName = ''

      console.log('所有视频状态重置完成')
    },

    // 处理浏览器关闭/刷新事件
    handleBeforeUnload() {
      console.log('检测到浏览器关闭/刷新，清理视频资源')

      // 同步执行视频资源清理（beforeunload事件中异步操作可能不会完成）
      try {
        // 快速清理大华视频
        if (this.isVideoInitialized && this.$refs.DWvideo && this.$refs.DWvideo.logOut) {
          this.$refs.DWvideo.logOut()
        }

        // 快速清理海康威视视频
        this.monitorCameraIndexCode = ''

        console.log('浏览器关闭前视频资源清理完成')
      } catch (error) {
        console.error('浏览器关闭前清理视频资源时发生错误:', error)
      }

      // 可选：显示确认对话框（某些浏览器支持）
      // event.preventDefault()
      // event.returnValue = '确定要离开吗？视频连接将被关闭。'
      // return event.returnValue
    },

    // 显示标签输入框
    showTagInputField(event) {
      event.stopPropagation()
      this.showTagInput = true
      this.newTagName = ''

      // 聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.tagInput) {
          this.$refs.tagInput.focus()
        }
      })
    },

    // 添加标签
    addTag() {
      if (this.newTagName.trim()) {
        // 添加新标签
        this.deviceInfo.tags.push(this.newTagName.trim())

        // 可以在这里保存标签到后端
        this.saveTagsToServer()

        // 清空输入框并隐藏
        this.newTagName = ''
        this.showTagInput = false
      } else {
        this.showTagInput = false
      }
    },

    // 删除标签
    removeTag(id, index) {
      this.deviceInfo.tags.splice(index, 1)

      // 可以在这里同步到后端
      this.deleteTagsToServer(id)

      this.$message.success('标签已删除')
    },

    // 处理输入框失焦事件
    handleTagInputBlur() {
      // 延迟执行，以防止与点击事件冲突
      setTimeout(() => {
        if (this.newTagName.trim()) {
          this.addTag()
        } else {
          this.showTagInput = false
        }
      }, 200)
    },

    // 处理点击输入框外部
    handleOutsideClick(event) {
      const tagContainer = document.querySelector('.tag-container')
      if (this.showTagInput && tagContainer && !tagContainer.contains(event.target)) {
        if (this.newTagName.trim()) {
          this.addTag()
        } else {
          this.showTagInput = false
        }
      }
    },

    // 保存标签到服务器
    saveTagsToServer() {
      // 这里应该实现保存标签到服务器的逻辑
      console.log('保存标签:', this.deviceInfo.tags, '设备ID:', this.currentDeviceId)

      // 示例API调用
      const params = {
        deviceCode: this.deviceCode,
        tag: this.newTagName,
      }
      addVideoTag(params)
        .then((res) => {
          if (res.code === 200) this.$message.success('标签添加成功')
        })
        .catch(() => {
          this.$message.error('标签保存失败')
        })
    },

    //标签删除
    deleteTagsToServer(id) {
      deleteVideoTag({ id: id })
        .then((res) => {
          if (res.code === 200) this.$message.success('标签删除成功')
        })
        .catch(() => {
          this.$message.error('标签删除失败')
        })
    },
  },
}
</script>

<style scoped lang="less">
.wrap-container {
  width: 100%;
  height: 100%;
  // height: 1058px;
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;

  .wrap-container-head {
    width: 80%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 38px;
    /deep/ .yearChange {
      .el-input__inner {
        height: 56px !important;
        background-color: #132c4e !important;
        border: 2px solid #afdcfb !important;
        color: #fff !important;
        border-radius: 15px !important;
      }
    }
    .wrap-container-tabs {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      width: 300px;
      .wrap-container-tab {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 32px;
        color: #ffffff;
        -webkit-text-stroke: 1px rgba(0, 0, 0, 0);
        text-align: center;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
      }
      .activeTab {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 32px;
        color: #ffffff;
        text-shadow: 0px 0px 15px #1677ff;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  // 主体内容区域
  .main-content-area {
    width: 90%;
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    height: calc(100vh - 500px);

    // 左侧视频区域
    .video-container {
      width: 70%;
      position: relative;
    }

    // 右侧设备信息区域
    .device-info-container {
      width: 28%;
      display: flex;
      flex-direction: column;
      gap: 20px;

      // 信息区块共同样式
      .info-section {
        background: rgba(19, 44, 78, 0.8);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #afdcfb;
      }

      // 设备信息区块
      .info-section {
        .info-title {
          font-size: 36px;
          color: #fff;
          font-weight: bold;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid rgba(175, 220, 251, 0.3);
        }

        .info-content {
          .info-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;

            .info-label {
              width: 140px;
              color: #afdcfb;
              font-size: 24px;
              margin-top: 4px;
            }

            .info-value {
              color: #fff;
              font-size: 24px;
              flex: 1;

              // 视频系统标识样式
              &.video-system-badge {
                padding: 4px 12px;
                border-radius: 6px;
                font-weight: bold;
                display: inline-block;

                &.dh {
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  color: #fff;
                }

                &.hk {
                  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                  color: #fff;
                }
              }

              // 摄像头编码样式
              &.camera-index-code {
                font-family: 'Courier New', monospace;
                background: rgba(102, 179, 255, 0.1);
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #66b3ff;
                color: #66b3ff;
              }
            }
          }

          // 标签相关样式
          .tag-item {
            align-items: flex-start;

            .tag-container {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              flex: 1;

              .info-tag {
                background: #409eff;
                color: #fff;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 24px;
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                .tag-close {
                  margin-left: 5px;
                  font-size: 18px;
                  cursor: pointer;

                  &:hover {
                    color: #ff4949;
                  }
                }
              }

              .inline-tag-input {
                margin-bottom: 8px;
                width: 200px;

                /deep/ .el-input__inner {
                  background-color: rgba(255, 255, 255, 0.1);
                  border: 1px solid #afdcfb;
                  color: #fff;
                  height: 38px;
                  font-size: 20px;
                }
              }

              .tag-button {
                display: flex;
                align-items: center;
                padding: 0 10px;
                height: 38px;
                font-size: 20px;
                margin-bottom: 8px;

                i {
                  margin-left: 5px;
                }
              }
            }
          }
        }
      }

      // 功能按钮区
      .function-buttons {
        display: flex;
        justify-content: space-between;

        .el-button {
          flex: 1;
          font-size: 24px;
        }
      }
    }
  }
}

// 云台控制面板
.ptz-control-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;

  .ptz-direction-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;

    .middle-row {
      display: flex;
      gap: 10px;
    }
  }

  .ptz-zoom-controls,
  .ptz-focus-controls {
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: center;
  }
}

:deep(.date-picker-up) {
  margin-top: -10px !important;
  margin-right: 1800px !important;
  transform-origin: center bottom !important;
}

// 监控页面样式
.monitor-container {
  width: 100%;
  height: 100%;
  color: #fff;
  // background: #0e0d33;

  .monitor-content {
    width: 100%;
    // padding: 20px;
    display: flex;
    flex-direction: column;

    .monitor-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 20px;
      margin-left: 20px;

      .play-back-btn {
        :deep(.el-input__inner) {
          background-color: #132c4e;
          border: 1px solid #2a3f5f;
          color: #fff;
        }
      }

      .change-btn {
        color: #409eff;
        font-size: 28px;
      }
    }

    .monitor-video {
      // 尺寸和变换由动态样式控制
      width: 2520px;
      height: 960px;
    }
    // .right-video {
    //   width: pxtorem(1350);
    //   height: calc(100% - 1.48438rem);
    //   margin: pxtorem(50) auto 0;
    //   background: rgba($color: #000, $alpha: 0.2);
    // }
    .monitor-history {
      height: 240px;

      .his-title {
        font-size: 32px;
        color: #fff;
        margin-bottom: 10px;
        padding-left: 10px;
      }

      .his-list {
        height: 320px;
        background: #132c4e;
        border-radius: 8px;
        padding: 20px;

        .history-items {
          display: flex;
          gap: 20px;
          height: 100%;
          overflow-x: auto;
          overflow-y: hidden;
          white-space: nowrap;
          padding-bottom: 10px;
        }

        .item {
          width: 360px;
          height: 280px;
          cursor: pointer;
          text-align: center;
          font-size: 28px;
          flex-shrink: 0;
          display: inline-block;

          &.empty-item {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: default;

            span {
              color: #999;
              font-size: 24px;
            }
          }

          .item-inner {
            width: 100%;
            height: 240px;
            background: #2a3f5f;
            border-radius: 4px;
            margin-bottom: 10px;
            transition: background-color 0.3s;

            &:hover {
              background: #409eff;
            }
          }

          .item-name {
            font-size: 24px;
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
