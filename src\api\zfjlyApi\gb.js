import { requestZfjly } from '@/utils/request'

/**
 * 执法记录仪国标(GB)相关API接口
 * 参考VideoPopNOGB的js封装模式
 */

// 获取登录基础信息
export function getkey(url, key) {
  return requestZfjly({
    url: `${url}/rest/index/login/get?key="${key}"`,
    method: 'get',
  })
}

// 获取用户信息
export function get_info(url) {
  return requestZfjly({
    url: `${url}/rest/user/user/get_info`,
    method: 'get',
  })
}

// 心跳检测
export function online(url) {
  return requestZfjly({
    url: `${url}/rest/other/user/online`,
    method: 'get',
  })
}

// 用户登录
export function login(url, data) {
  return requestZfjly({
    url: `${url}/rest/index/login/login`,
    method: 'post',
    data
  })
}

// 获取国标设备信息
export function getDeviceGB(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/getdevice`,
    method: 'post',
    data
  })
}

// 开始国标视频流
export function startLiveGB(url, data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/startLive`,
    method: 'post',
    data
  })
}

// 停止国标视频流
export function stopLiveGB(url, data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/stopLive`,
    method: 'post',
    data
  })
}

// 开始国标音频流
export function startAudioGB(url, data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/startAudio`,
    method: 'post',
    data
  })
}

// 停止国标音频流
export function stopAudioGB(url, data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/stopAudio`,
    method: 'post',
    data
  })
}

// 发送国标设备命令
export function send_cmd_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/gis/gismoni/send_cmd`,
    method: 'post',
    data
  })
}

// 获取国标设备列表
export function gdlist_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/other/unitjson/gdlist`,
    method: 'post',
    data
  })
}

// 获取国标设备详细列表
export function gdlist_dv_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/other/unitjson/gdlist_dv`,
    method: 'post',
    data
  })
}

// 获取国标设备位置信息
export function get_point_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/gis/gismoni/get_point`,
    method: 'post',
    data
  })
}

// 国标设备预设点控制
export function preset_control_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/preset`,
    method: 'post',
    data
  })
}

// 国标设备云台控制
export function ptz_control_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/ptz`,
    method: 'post',
    data
  })
}

// 国标设备录像查询
export function record_query_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/record/query`,
    method: 'post',
    data
  })
}

// 国标设备录像回放
export function record_playback_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/record/playback`,
    method: 'post',
    data
  })
}

// 国标设备录像下载
export function record_download_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/record/download`,
    method: 'post',
    data
  })
}

// 国标设备状态查询
export function device_status_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/device/status`,
    method: 'post',
    data
  })
}

// 国标设备信息查询
export function device_info_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/device/info`,
    method: 'post',
    data
  })
}

// 国标设备目录查询
export function catalog_query_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/catalog/query`,
    method: 'post',
    data
  })
}

// 国标设备订阅
export function subscribe_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/subscribe`,
    method: 'post',
    data
  })
}

// 国标设备取消订阅
export function unsubscribe_gb(url, data) {
  return requestZfjly({
    url: `${url}/rest/monitor/videopatrols/unsubscribe`,
    method: 'post',
    data
  })
}
