import { requestZfjly } from '@/utils/request'

export function getkey(url,key) {
  return requestZfjly({
    url: `${url}/rest/index/login/get?key="${key}"`,
    method: 'get',
  })
}

export function get_info(url) {
  return requestZfjly({
    url: `${url}/rest/user/user/get_info`,
    method: 'get',
  })
}

export function online(url) {
  return requestZfjly({
    url: `${url}/rest/other/user/online`,
    method: 'get',
  })
}

export function login(url,data) {
  return requestZfjly({
    url: `${url}/rest/index/login/login`,
    method: 'post',
    data
  })
}

export function startLive(url,data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/startLive`,
    method: 'post',
    data
  })
}

export function stopLive(url,data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/stopLive`,
    method: 'post',
    data
  })
}

export function startAudio(url,data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/startAudio`,
    method: 'post',
    data
  })
}

export function stopAudio(url,data) {
  return requestZfjly({
    url: `${url}/rest/live/chrome/stopAudio`,
    method: 'post',
    data
  })
}

export function send_cmd(url,data) {
  return requestZfjly({
    url: `${url}/rest/gis/gismoni/send_cmd`,
    method: 'post',
    data
  })
}

export function gdlist_dv(url,data) {
  return requestZfjly({
    url: `${url}/rest/other/unitjson/gdlist_dv`,
    method: 'post',
    data
  })
}

export function gdlist(url,data) {
  return requestZfjly({
    url: `${url}/rest/other/unitjson/gdlist`,
    method: 'post',
    data
  })
}

export function get_point(url,data) {
  return requestZfjly({
    url: `${url}/rest/gis/gismoni/get_point`,
    method: 'post',
    data
  })
}